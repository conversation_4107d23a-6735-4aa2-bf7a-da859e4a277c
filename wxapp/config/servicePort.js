/**
 * @description: 配置文件
 * @author: zzn
 * @Date: 2025-03-05
 */

// 环境配置
const ENV_CONFIG = {
  // 开发环境配置，可自由拓展  
  development: {
    WxApiRoot: 'https://api-pro.naiterui.com/', //接口API地址
    ImgUrl: 'https://patient-pro.naiterui.com/', //图片地址，小程序内图片需要放到服务器上
    WebViewUrl: 'https://patient-pro.naiterui.com' // H5 Webview跳转域名
  },
  // 测试环境配置
  staging: {
    WxApiRoot: 'https://api-pro.demo.com/', //接口API地址
    ImgUrl: 'https://patient-pro.demo.com/', //图片地址，小程序内图片需要放到服务器上
    WebViewUrl: 'https://patient-pro.demo.com' // H5 Webview跳转域名
  },
  // 生产环境配置
  production: {
    WxApiRoot: 'https://api.demo.com/', //接口API地址
    ImgUrl: 'https://patient.demo.com/', //图片地址，小程序内图片需要放到服务器上
    WebViewUrl: 'https://patient.demo.com' // H5 Webview跳转域名
  }
}

// 获取当前环境 envVersion 为 develop 为开发版；trial 为体验版；release 为正式版
const getEnvVersion = () => {
  const { miniProgram: { envVersion, appId } } = wx.getAccountInfoSync()
  console.log('miniProgram:', envVersion, appId)
  switch (envVersion) {
    case 'develop':
      return 'development'
    case 'trial':
      return 'staging'
    case 'release':
      return 'production'
    default:
      return 'development'
  }
}

// 获取当前环境的配置
const currentEnv = getEnvVersion()
const config = ENV_CONFIG[currentEnv]
// 将配置导出
module.exports = config
