// 以下是业务服务器API地址
// 本机开发时使用
import { WxApiRoot, ImgUrl, WebViewUrl } from './api-prod'
module.exports = {
  mqttuser: 'miniappuser',
  mqttpass: '201MoCf3Jf3es8123asF2+s',
  webRoot: WxApiRoot,
  ImgUrl: ImgUrl,
  WebViewUrl: WebViewUrl,
  AuthLoginByWeixin: WxApiRoot + 'weixin/login/miniapp/', //GET 微信登录
  AuthRegister: WxApiRoot + 'weixin/login/miniapp/', //POST 账号注册
  AuthPhone: WxApiRoot + 'weixin/login/miniapp/phone/', //POST 获取手机号
  consultText: WxApiRoot + 'weixin/consult/session/text/list', //GET 图文咨询列表
  consultVideo: WxApiRoot + 'weixin/consult/video/list', //GET 视频咨询列表
  videoConsult: WxApiRoot + 'vc/video/consult/patient/consult/record', // get 视频问诊列表
  getImageList: WxApiRoot + 'vc/video/consult/inRoom', // get 视频问诊图片消息列表

  userInfo: WxApiRoot + 'user/info', // GET 用户基本信息
  Images: WxApiRoot + 'vc/video/consult/upload', // 图片服务
  getAppIdAndKey: WxApiRoot + 'vc/video/consult/getAppIdAndKey', // 获取腾讯云配置信息
  recordList: WxApiRoot + 'vc/video/consult/patient/consult/record', // 问诊记录
  stayRecordList: WxApiRoot + 'vc/video/consult/patient/consult/record/test', // 待接诊记录
  sendGroupMsg: WxApiRoot + 'vc/video/consult/sendGroupMsg', // 发送群组消息
  finish: WxApiRoot + 'vc/video/consult/finish', // 结束问诊

  uplodeFile: WxApiRoot + 'im/chat/media/upload', //sendMessage
  getConnectParams: WxApiRoot + 'im/connect/params', //mqtt连接参数
  sendMessage: WxApiRoot + 'im/chat/sendMessage', //发送消息
  sessionList: WxApiRoot + 'im/session/list', //问诊倒计时
  chatHistory: WxApiRoot + 'im/chat/history', // post 图文问诊记录
  getStatus: WxApiRoot + 'im/session/status', // get 获取系统时间

  orderCreate: WxApiRoot + 'b2c/orders', // post 下单请求接口
  orderList: WxApiRoot + 'b2c/orders', // get 订单列表
  orderDetail: WxApiRoot + 'b2c/orders/{0}', // get 订单详情
  orderCancel: WxApiRoot + 'b2c/orders/cancel/{0}', // get 取消订单
  orderPay: WxApiRoot + 'b2c/orders/pay/{0}', // get 支付订单
  orderShipping: WxApiRoot + 'b2c/orders/{0}/shipping', // get 订单物流
  statisticalInfo: WxApiRoot + 'b2c/orders/statisticalInfo', //订单角标数量

  cityList: WxApiRoot + 'ap/patient/cacheData', //科室与地区
  myDoctor: WxApiRoot + 'ap/patient/doctor/my', //首页-我的医生
  chat: WxApiRoot + 'ap/patient/consult/chat', //问诊前判断接口
  chatvideo: WxApiRoot + 'ap/patient/consult/video', //问诊前判断接口视频
  doctorDetail: WxApiRoot + 'ap/patient/doctor/detail', //医生详情
  clinic: WxApiRoot + 'ap/patient/doctor/clinic', //专家名医列表
  clinicDetail: WxApiRoot + 'ap/patient/doctor/clinic/detail', //专家名医详情
  peopleList: WxApiRoot + 'ap/patient/inquirer/list', //就诊人列表
  addPeople: WxApiRoot + 'ap/patient/inquirer/add', //新增就诊人
  relationList: WxApiRoot + 'ap/patient/inquirer/relation/list', //关系字典表
  delPeople: WxApiRoot + 'ap/patient/inquirer/del', //删除就诊人
  peopleDetail: WxApiRoot + 'ap/patient/inquirer/detail', //就诊人详情
  evalList: WxApiRoot + 'ap/patient/consult/comment/list', //医生评价
  submitComment: WxApiRoot + 'ap/patient/consult/comment/submit', //POST 医生评价
  doctorSimple: WxApiRoot + 'ap/patient/doctor/simple', // get 医生基本信息
  diseaseAdd: WxApiRoot + 'emr/patient/disease/add', //就诊人病情添加
  uploadImg: WxApiRoot + 'emr/patient/disease/img/upload', //添加图片接口
  disclaimer: WxApiRoot + 'ap/patient/consult/disclaimer', //判断是否需要支付
  clinicList: WxApiRoot + 'ap/patient/doctor/clinic', //科室医生列表
  diseaseList: WxApiRoot + 'ap/common/diseases', //常见疾病列表
  // 地址管理
  addresesList: WxApiRoot + 'b2c/addreses', // get 地址列表
  addresesDelete: WxApiRoot + 'b2c/addreses/delete', // post 删除地址
  addresesSave: WxApiRoot + 'b2c/addreses/save', // post 保存地址
  addresesDetail: WxApiRoot + 'b2c/addreses/detail', // get 地址详情
  getCitys: WxApiRoot + 'b2c/addreses/citys', // get 城市地区

  prescriptionList: WxApiRoot + 'ap/patient/recommend/list', // 处方列表
  prescriptionDetail: WxApiRoot + 'recommend/prescription/patient/detail', // 处方详情
  prescriptionReapply: WxApiRoot + 'recommend/medication/require/patient/again', // 再次购买
  settlements: WxApiRoot + 'b2c/carts/recom/settlements', //处方确定购买页面详情

  caseDetail: WxApiRoot + 'emr/patient/case/detail', // post 病例详情
  payinfo: WxApiRoot + 'ap/patient/consult/payinfo', //判断是否支付
  videoPayInfo: WxApiRoot + 'ap/patient/consult/video/payinfo', //发起视频问诊支付
  templates: WxApiRoot + 'weixin/index/subscribe/templates', //模板消息
  illnessDetail: WxApiRoot + 'emr/patient/disease/detail', //病情详情
  valid: WxApiRoot + 'ap/patient/consult/getCommentComplaint', //判断是否可以评价
  getBaseInfo: WxApiRoot + 'ap/patient/base', //用户信息获取
  citydepart: WxApiRoot + 'ap/patient/cacheData', //获取科室信息
  getPhoneNum: WxApiRoot + 'ap/index', //获取客服电话
  drugremind: WxApiRoot + 'recommend/drugremind', //get 用药提醒设置信息

  getFollowupList: WxApiRoot + 'ap/followup', //get 随访列表
  getFollowupStatus: WxApiRoot + 'ap/followup/check', //get 查询随访状态
  getMonthFollowup: WxApiRoot + 'user/ap/followup/month', //get 获取当月的随访计划
  getTodayFollowup: WxApiRoot + 'user/ap/followup/today', //get 获取当日的随访计划
  getFollowVisitList: WxApiRoot + 'user/ap/followup/subsequent/visit/detail', //get 获取复诊计划列表
  getFollowDetail: WxApiRoot + 'user/ap/followup/detail', //get 随访&复诊详情
  inspectPay: WxApiRoot + 'user/ap/self/inspect/wx/pay', //get 支付自检
  inspectRefund: WxApiRoot + 'user/ap/self/inspect/wx/refund', //get 退款自检
  // 中药相关
  prescriptionTcmDetail: WxApiRoot + 'recommend/prescription/patient/tcm/detail', //post 中药处方详情
  tcmOrderDetail: WxApiRoot + 'b2c/carts/tcm/settlements', //post 中药订单详情
  settlementInfo: WxApiRoot + 'b2c/carts/tcm/settlements', // 中药处方确认订单 获取信息
  preOrder: WxApiRoot + 'b2c/orders/tcm/create', // 中药订单创建
  noticeList: WxApiRoot + 'ap/notice/message/list', //get 院务公开列表
  noticeDetail: WxApiRoot + 'ap/notice/message', //get 院务公开详情
  inquirerFace: WxApiRoot + 'ap/patient/inquirer/face', //get 人脸核身后调用
  groupsList: WxApiRoot + 'ap/notice/message/groups', //get 新闻资讯分组列表
  articleList: WxApiRoot + 'ap/notice/message/list', //get 新闻资讯列表
  articleDetail: WxApiRoot + 'ap/notice/message', //get 新闻资讯详情
  getArticleDetail: WxApiRoot + 'ap/agreement', //get 协议相关内容
  uplodPhoto: WxApiRoot + 'ap/patient/photo', //post 上传头像
  // 咨询师
  counselorList: WxApiRoot + 'counselor/counselor/list', //get 咨询师列表
  counselorInfo: WxApiRoot + 'counselor/counselor/info', //get 咨询师详情
  counselorConsult: WxApiRoot + 'counselor/pre/consult/list', //get 咨询师咨询列表
  sendPreConsult: WxApiRoot + 'counselor/pre/consult/sendPreConsult', //post 咨询师发送消息
  consultupload: WxApiRoot + 'counselor/pre/consult/upload', //post 图片消息上传
  startPreConsult: WxApiRoot + 'counselor/pre/consult/startPreConsult', //post 发起咨询
  consultDetail: WxApiRoot + 'counselor/pre/consult/detail', //get 聊天详情
  formPage: WxApiRoot + 'user/ap/counselor/form/counselor/form/page', //患者我的咨询分页
  conditionList: WxApiRoot + 'medical/b2c/carts/conditionList',//可自提门店信息列表

  bannerList: WxApiRoot + 'ap/banner/list', //get banner列表
  bannerDetail: WxApiRoot + 'ap/banner', //get banner详情
  consultOrderList: WxApiRoot + 'ap/consult/order/page', //get 问诊订单列表
  consultOrderDetail: WxApiRoot + 'ap/consult/order/detail', //get 问诊订单详情
  productList: WxApiRoot + 'medical/b2c/product/list',
  mallSections: WxApiRoot + 'medical/b2c/mall/sections',
  addCarts: WxApiRoot + 'medical/b2c/carts/add', // 商城添加购物车
  cartsInfo: WxApiRoot + 'medical/b2c/carts/info', // 获取购物车列表
  productDetail: WxApiRoot + 'medical/b2c/product/detail', // 获取商品详情
  updateQuantity: WxApiRoot + 'medical/b2c/carts/update/quantity', // 修改购物车商品数量
  cartsDelete: WxApiRoot + 'medical/b2c/carts/delete', //删除购物车
  updateSelected: WxApiRoot + 'medical/b2c/carts/update/selected', // 修改选中
  categoryList: WxApiRoot + 'medical/b2c/category/list', // 获取分类列表
  cartsSettlements: WxApiRoot + 'medical/b2c/carts/settlements', // 结算中心
  b2cOrders: WxApiRoot + 'medical/b2c/orders',
  ordersPay: WxApiRoot + 'medical/b2c/orders/pay/',
  ordersPayDetail: WxApiRoot + 'medical/b2c/orders/ap', // 代付订单详情
  caseList: WxApiRoot + 'user/ap/patient/case/list',
  // 患者关系绑定
  patientRelationBind: WxApiRoot + 'ap/patient/relation/bind', // POST
  // 专区分页列表
  zoneList: WxApiRoot + 'medical/b2c/mall/sections/zone', // GET
  // 专区商品详情
  zoneDetail: WxApiRoot + `medical/b2c/mall/sections/zone/{0}`, // GET
  // 专区文章详情
  zoneArticleDetail: WxApiRoot + `medical/b2c/mall/sections/{0}/content`,
  // 专区商品搜索提示词
  zoneSearchTips: WxApiRoot + 'medical/b2c/mall/sections/zone/{0}/suggest',
  // HPV服务产品
  hpvProductList: WxApiRoot + 'user/ap/patient/hpv/product/list',
  // HPV服务产品详情
  hpvProduct: WxApiRoot + 'user/ap/patient/hpv/product',
  // HPV活动列表
  hpvActivityList: WxApiRoot + 'user/ap/patient/hpv/activity/list',
  // HPV活动详情
  hpvActivity: WxApiRoot + 'user/ap/patient/hpv/activity'
}
