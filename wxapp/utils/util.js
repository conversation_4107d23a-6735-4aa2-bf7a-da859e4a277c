// mqtt连接参数
const ConnectParamsKey = 'CONNECT_KEY'
const api = require('../config/api.js')
// 时间类工具
const date = require('./date')
// 计算类工具
const math = require('./math')
// 常用类工具
const tools = require('./tools')
// 正则校验类工具
const check = require('./check')
// 用户常用方法
const user = require('./user')
// 公共异步方法
const common = require('./common')
// 获取系统信息
const sysInfo = wx.getSystemInfoSync()
// 主题颜色
const THEMECOLOR = '#2893FF'
const headerParams = {
  // '_p': 0,
  '_m': sysInfo.model,
  '_o': 0,
  '_w': 1
}
// 消息分组常量（两分钟为一个分组）
const LIMIT_GROUP_TIME = 120000

var log = wx.getRealtimeLogManager ? wx.getRealtimeLogManager() : null //log日志文件向微信后台发送
const logInfo = {
  debug() {
    if (!log) return
    log.debug.apply(log, arguments)
  },
  info() {
    if (!log) return
    log.info.apply(log, arguments)
  },
  warn() {
    if (!log) return
    log.warn.apply(log, arguments)
  },
  error() {
    if (!log) return
    log.error.apply(log, arguments)
  },
  setFilterMsg(msg) { // 从基础库2.7.3开始支持
    if (!log || !log.setFilterMsg) return
    if (typeof msg !== 'string') return
    log.setFilterMsg(msg)
  }
}
/**
 * 封封微信的的request
 */
function request(url, data = {}, method = 'GET', headers = 1, needLogin = true) {
  return new Promise((resolve, reject) => {
    const parmas = {}
    Object.keys(data).forEach(key => {
      if (data[key] !== void 0 && data[key] !== null) {
        parmas[key] = data[key]
      }
    })
    if (wx.getStorageSync('token') || !needLogin) {
      _request(url, parmas, method, headers, resolve, reject)
    } else {
      user.loginByWeixin().then(response => {
        if (response.data.code === 0 && response.data.data.loginStatus === 1) {
          // 切换到登录页面
          // const page = getCurrentPages()
          // var currentPage = page[page.length - 1]
          // wx.navigateTo({
          //   url: '/pages/auth/login/login'
          // })
          goLogin()

          reject(response)
        } else {
          _request(url, parmas, method, headers, resolve, reject)
        }
      }).catch((err) => {
        reject(err)
      })
    }

  })
}

function _request(url, data, method, headers, resolve, reject) {
  const tokenKey = wx.getStorageSync('tokenKey')
  let ContentType = ''
  if (headers === 1) {
    ContentType = 'application/json'
  } else if (headers === 3) {
    // 临时新增_v
    headerParams._v = '1.0.1'
  } else if(headers === 6){
    ContentType = 'multipart/form-data; boundary=XXX'
  }else{
    ContentType = 'application/x-www-form-urlencoded;charset=utf-8'
  }
  const header = {
    'Content-Type': ContentType
  }
  if (wx.getStorageSync('token')) {
    header[tokenKey] = wx.getStorageSync('token')
  }
  wx.request({
    url: url,
    data: data,
    method: method,
    header: Object.assign(header, headerParams),
    success: response => {
      logInfo.info('request:', url, data, response.data.code, response.data.msg)
      console.log('入参params:>>', data)
      console.log("request请求", method, url, response.data.code, response.data, Object.assign(header, headerParams))
      if (response.data.code === 1) {
        wx.clearStorage()
        user.loginByWeixin().then(res => {
          if (res.data.code === 0 && res.data.data && res.data.data.loginStatus === 1) {
            // 切换到登录页面
            // wx.navigateTo({
            //   url: '/pages/auth/login/login'
            // })
            goLogin()
            reject(res.data.msg || new Error('登陆失败'))
          } else {
            _request(url, data, method, headers, resolve, reject)
          }
        }).catch(err => {
          console.log('失败了')
          reject(err)
        })
      } else if (response.data.code === 0 && response.data.data && response.data.data.loginStatus === 0) {
        //存储用户信息
        const app = getApp()
        app.globalData.loginNum = 0
        response.data.data.userInfo.unionId = response.data.data.unionId
        user.setUserInfo(response.data.data.userInfo)
        wx.setStorageSync('tokenKey', response.data.data.userInfo.tokenKey)
        wx.setStorageSync('token', response.data.data.userInfo.token)
        app.globalData.userInfo = response.data.data.userInfo
        user.loginSuccess()
        resolve(response)
      } else if (response.data.code === 0 && response.data.data && response.data.data.loginStatus === 1) {
        const app = getApp()
        app.globalData.loginNum = 0
        response.data.data.userInfo.unionId = response.data.data.unionId
        user.setUserInfo(response.data.data.userInfo)
        app.globalData.userInfo = response.data.data.userInfo
        resolve(response)
      } else if (response.data.code === 19100) {
        // 清除登录相关内容
        wx.removeStorageSync('userInfo')
        wx.removeStorageSync('tokenKey')
        wx.removeStorageSync('token')
        reject(response.data.msg || new Error('登陆失败'))
      } else {
        resolve(response)
      }
    },
    fail: function(err) {
      wx.showToast({
        title: '网络连接异常',
        icon: 'none'
      })
      logInfo.error('request:', url, data, err)
      reject(err)
    }
  })
}

function showErrorToast(msg) {
  wx.showToast({
    title: msg,
    image: '/static/images/icon_error.png'
  })
}

function showToast(obj) {
  var data = {}
  data.title = obj.title
  data.icon = obj.icon || 'none'
  if (obj.duration) {
    data.duration = obj.duration
  }
  if (obj.image) {
    data.image = obj.image
  }
  data.mask = obj.mask || false
  if (obj.success) {
    data.success = obj.success
  }
  if (obj.fail) {
    data.fail = obj.fail
  }
  if (obj.complete) {
    data.complete = obj.complete
  }
  wx.showToast(data)
}

function makePhoneCall(phoneNumber) {
  const info = wx.getSystemInfoSync()
  if (info.platform === 'android') {
    // android 需要执行的代码
    wx.showActionSheet({
      itemList: [phoneNumber],
      success(res) {
        wx.makePhoneCall({
          phoneNumber: phoneNumber
        })
      },
      fail(res) {
        console.log(res.errMsg)
      }
    })
  } else {
    // ios 需要执行的代码
    wx.makePhoneCall({
      phoneNumber: phoneNumber
    })
  }
}
//mqtt消息到达更新
function onMessageArrived() {
  const pages = getCurrentPages()
  if (pages.length > 0) {
    const page = pages[pages.length - 1]
    if ('onMessageArrived' in page) {
      page.onMessageArrived.apply(page, arguments)
    }
  }
}
// 设置用户聊天数据
function setChatData(key, data) {
  var cdata = getChatData(key)
  if (cdata) {
    const sendTime = cdata[cdata.length - 1].messages[cdata[cdata.length - 1].messages.length - 1].sendTime
    if (data.sendTime - sendTime <= LIMIT_GROUP_TIME) {
      cdata[cdata.length - 1].messages.unshift(data)
      cdata[cdata.length - 1].sendTime = data.sendTime
    } else {
      cdata.push({
        messages: [data],
        timeGroup: data.sendTime,
        sendTime: data.sendTime
      })
    }
  } else {
    cdata = [{
      messages: [data],
      timeGroup: data.sendTime,
      sendTime: data.sendTime
    }]
  }

  if (cdata) {
    //设置缓存数据条数限制
    if (cdata.length >= 100) {
      cdata.shift()
    }
    // wx.getStorageSync(key,[...cdata, data]);
  }
  wx.setStorageSync(key, cdata)

}

// 获取用户聊天数据
function getChatData(key) {
  const data = wx.getStorageSync(key)
  return data || ''
}
/**
 *
 * @returns android ios
 */
function getPlatform() {
  const info = wx.getSystemInfoSync()
  return info.platform
}

function getConnectParams(app) {
  if (!app.globalData.connectParams) {
    app.globalData.connectParams = wx.getStorageSync(ConnectParamsKey)
  }
  return app.globalData.connectParams
}

function setConnectParams(app, params) {
  app.globalData.connectParams = params
  app.globalData.connectParams.clientId = 'c_pt_' + app.globalData.userInfo.userId
  app.globalData.connectParams.msgTopicName = app.globalData.connectParams.msgTopicName
  wx.setStorageSync(ConnectParamsKey, app.globalData.connectParams)
}

function showModal(object) {
  object.confirmColor = THEMECOLOR
  wx.showModal(object)
}

function showLoading(obj) {
  obj.title = obj.title ? obj.title : '加载中'
  obj.mask = true
  wx.showLoading(obj)
}

function hideLoading() {
  wx.hideLoading()
}

function hideToast() {
  wx.hideToast()
}

function goLogin() {
  const page = getCurrentPages()
  const currentPage = page[page.length - 1]
  if (currentPage.route !== 'pages/auth/login/login') {
    wx.navigateTo({
      url: '/pages/auth/login/login'
    })
  }
}

module.exports = {
  showErrorToast,
  showToast,
  makePhoneCall,
  onMessageArrived,
  setChatData,
  getChatData,
  headerParams,
  getPlatform,
  getConnectParams,
  setConnectParams,
  showModal,
  showLoading,
  hideLoading,
  THEMECOLOR,
  logInfo,
  hideToast,
  _request,
  request,
  goLogin,
  ...date,
  ...math,
  ...check,
  ...tools,
  ...user,
  ...common
}
