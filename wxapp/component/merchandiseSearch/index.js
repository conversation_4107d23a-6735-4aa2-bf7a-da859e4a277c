var api = require('../../config/api.js')
const util = require('../../utils/util')
const app = getApp()

Component({
  properties: {
    placeholder: {
      type: String,
      value: '药品通用名/商品名称/商品名'
    },
    readonly: {
      type: <PERSON>olean,
      value: false
    },
    showSearchBtn: {
      type: Boolean,
      value: false
    },
    onclearBack: {
      type: Boolean,
      value: false
    },
    showRelevant: {
      type: <PERSON><PERSON>an,
      value: false
    },
    transparent: {
      type: String,
      value: '#fff'
    },
    shape: {
      type: String,
      value: 'square'
    },
    value: {
      type: String,
      observer: function(val) {
        this.setData({
          text: val || ''
        })
      }
    }
  },
  data: {
    text: '',
    relevantList: [],
    ic_input_search: api.ImgUrl + 'images/<EMAIL>',
    scrollViewHeight: 0,
    showList: false
  },
  lifetimes: {
    attached() {
      if (this.properties.value) {
        this.setData({
          text: this.properties.value
        })
      }
      this.getDom()
    }
  },
  methods: {
    getDom() {
      const query = this.createSelectorQuery()
      query.select('#searchWrap').boundingClientRect()
      query.exec(res => {
        const data = res[0]
        this.setData({
          scrollViewHeight: app.globalData.screenHeight - (data.top + data.height + app.globalData.navBarHeight)
        })
      })
    },
    goSearch: util.throttle(() => {
      if (this.properties.readonly) {
        wx.navigateTo({
          url: '/pages/searchMerchandise/index'
        })
      }
    }, 300),
    onSearch() {
      if (!this.data.text) {
        return
      }
      if (this.properties.value) {
        this.setData({
          showList: false,
          relevantList: []
        })
        this.triggerEvent('search', this.data.text)
      } else {
        this.saveHistory(this.data.text)
        this.goResult(this.data.text)
      }
    },
    onChange(e) {
      if (!e.detail) {
        this.setData({
          relevantList: []
        })
        return
      }
      this.setData({
        showList: true,
        text: e.detail
      })
      this.search(e.detail)
    },
    search(keyword) {
      util.request(api.productList, {
        keyword,
        page: 1,
        offset: 1000
      }, 'get').then(res => {
        const list = res.data.data.result
        if (list && list.length > 0) {
          list.forEach((item) => {
            item.matchText = this.formatSearch(item.name, keyword)
          })
        }
        this.setData({
          relevantList: list
        })
      })
    },
    clearText() {
      if (this.properties.onclearBack) {
        wx.navigateBack()
      }
      this.setData({
        showList: false,
        relevantList: []
      })
    },
    handleClick(e) {
      const text = e.currentTarget.dataset.item.name
      if (this.properties.value) {
        this.setData({
          text,
          showList: false,
          relevantList: []
        })
        this.triggerEvent('search', text)
      } else {
        this.saveHistory(text)
        this.goResult(text)
      }
    },
    goResult(text) {
      wx.navigateTo({
        url: '/pages/searchMerchandiseResult/index?text=' + text
      })
    },
    saveHistory(text) {
      let searchHistory = wx.getStorageSync('searchHistory') || []
      if (searchHistory.findIndex(item => item === text) === -1) {
        searchHistory.unshift(text)
        if (searchHistory.length > 15) {
          searchHistory = searchHistory.splice(0, 15)
        }
        wx.setStorageSync('searchHistory', searchHistory)
      }
    },
    formatSearch(origin, search) {
      return origin.replace(new RegExp(search, 'ig'), function($1) {
        return `<span class="match">${$1}</span>`
      })
    }
  }
})
