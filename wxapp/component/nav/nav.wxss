.nav-wrap {
    position: fixed;
    top: 0;
    left: 0;
    width: 750rpx;
    z-index: 99999;
}

.content {
    position: relative;
    width: 100%;
    height: 44px;
    /* border:1px solid #191919; */
}

.back{
   width: 44rpx;
	 height: 44rpx;
	 z-index: 999999;
	 margin-left: 20rpx;
}
.home{
   width: 44rpx;
	 height: 44rpx;
	 z-index: 999999;
	 margin-right: 20rpx;
}
.lineblack{
	height: 36rpx;
	background: #B0B0B0;
	width: 1rpx;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%,-50%);
	opacity: 0.5;
}
.lineWhite{
	height: 36rpx;
	background: rgba(255,255,255,.7);
	width: 1rpx;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%,-50%);
	opacity: 0.5;
}
.title{text-align:center;font-weight: 900;font-size: 32rpx;}
.letBtnBox{
	width: 100px;
    display: flex;
    align-items: center;
}

.capsule-box{
    width: 85px;
    border-radius: 88rpx;
    background: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.isWhite{
	border: 1rpx solid rgba(255,255,255,.25);
	background: rgba(0,0,0,.15);
}
.isblack{
	border: 1rpx solid rgba(0,0,0,.1);
	background: rgba(255,255,255,.6);
}
.opacity{
    background: rgba(0,0,0,.1);
}