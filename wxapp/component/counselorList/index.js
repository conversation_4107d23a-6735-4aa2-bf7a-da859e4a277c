/**
 * index
 */
var api = require('../../config/api.js')
const app = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    /**
     * 列表数据
     * */
    list: {
      type: Array,
      value: []
    },
    /**
     * 1.图文咨询
     * 2.视频复诊
     * 3.我的医生
     */
    type: {
      type: Number,
      value: 0
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    doctorName: '',
    doctorId: '',
    tapTime: '',
    imgObject: {
      img_blank_doctor: api.ImgUrl + 'images/img_blank_doctor.png'
    },
    currentType: 0,
    counselorId:'',
    userInfo:''
  },
  attached() {
    const userInfo = wx.getStorageSync('userInfo') || null
    this.authToast = this.selectComponent('#authToast') //订阅消息二次弹窗
    console.log(this.authToast, 40)
    this.setData({
      userInfo
    })
    // this.histoast = this.selectComponent('#histoast'); //his就诊记录弹窗
  },
  /**
   * 组件的方法列表
   */
  methods: {
    // 发起问诊
    handleConsult(e) {
      console.log(e,'123')
      var nowTime = new Date()
      if (nowTime - this.data.tapTime < 1000) {
        console.log('阻断')
        return
      }
      this.setData({
        tapTime: nowTime,
        counselorName: e.currentTarget.dataset.name,
        counselorId: e.currentTarget.dataset.id
      })
      this.authToast.seeCounselor(e)
    },
    onAuthSub() {
      console.log('执行了')
      console.log(this.authToast.data, this.authToast.data.isSwatchOff)
      if (!this.authToast.data.isSwatchOff) {
        wx.requestSubscribeMessage({
          tmplIds: app.globalData.templateId,
          success: () => {
            this.authToast.openCounselorChat({
              patientId: this.data.userInfo.userId,
              counselorId: this.data.counselorId
            })
          },
          fail: (res) => {
            console.log('onAuthSub', 'fail')
            this.authToast.openCounselorChat({
              patientId: this.data.userInfo.userId,
              counselorId: this.data.counselorId
            })
          }
        })
      } else {
        wx.openSetting({
          success: (res) => {
            this.authToast.setData({
              isSwatchOff: false,
              authShow: false,
              clickFlag: true
            })
          }
        })
      }
    },
    onClose() {
      console.log('执行了')
      this.authToast.setData({
        authShow: false,
        clickFlag: true
      })
      this.authToast.openCounselorChat({
        patientId: this.data.userInfo.userId,
        counselorId: this.data.counselorId
      })
    },
    // 咨询记录
    counselHistory(e) {
      const id = e.currentTarget.dataset.id
      wx.navigateTo({
        url: '/pages/consult/record/index?doctorId=' + id
      })
    }
  }
})
