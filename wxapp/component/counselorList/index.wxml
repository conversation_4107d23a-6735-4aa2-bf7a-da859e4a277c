<view class="" hover-class="none" hover-stop-propagation="false">
	<view class="list pb1" wx:if="{{list.length>0}}">
		<view wx:for="{{list}}" wx:for-item="item" wx:key="*this">
			<view open-type='navigate'
			 hover-class="none"
				class="item bg-color-white m20 p20">
				<view class="info flex">
					<view class="photo">
						<image src="{{item.avatar ? item.avatar : '/static/images/counselor_default_avatar.png'}}" mode="aspectFill"
							class="imgBlock"></image>
					</view>
					<view class="ml20 flex1">
						<view class="name f32 b c333">{{item.name}}<text class="ml20 n c666 f28">{{item.positionName ? item.positionName :'' }}</text>
						</view>
						<view class="text c666" wx:if="{{item.personalProfile}}">
							<view>擅长：{{item.personalProfile ? item.personalProfile : '-'}}</view>
						</view>
						<view class="btn flex_m">
							<view class="flex_c_m" catchtap="handleConsult" data-name='{{item.name}}' data-id="{{item.counselorId}}"
								data-templateId='{{templateId}}'>
								<view class="c666">
									免费咨询
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
	<!-- 无数据 -->
	<view class="noData" style="margin-top: 120rpx;" wx:else>
		<image src="{{imgObject.img_blank_doctor}}"></image>
		<view class="tc c666 f28 pt40 pb40">暂无咨询师</view>
	</view>
</view>
	<authorization id='authToast' bind:authSub='onAuthSub' bind:close='onClose'></authorization>