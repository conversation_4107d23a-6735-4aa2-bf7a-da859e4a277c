// component/medicalHistory/medicalHistory.js
var api = require('../../config/api.js')
const util = require('../../utils/util.js')
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否是详情页面
    isDetail: {
      type: Boolean,
      value: true
    },
    // 过敏史
    allergy: {
      type: Object,
      value: {}
    },
    // 既往史
    always: {
      type: Object,
      value: {}
    },
    // 家庭史
    family: {
      type: Object,
      value: {}
    },
    // 个人史
    personal: {
      type: Object,
      value: {}
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    allergy: '',
    always: '',
    family: '',
    personal: '',
    showPicker: false,
    imgObject: {
      close: api.ImgUrl + 'images/ic_close_01.png'
    },
    ywgm: ['青霉素过敏', '磺胺过敏', '泛影葡胺过敏', '地卡因过敏', '链霉素过敏'],
    jwjb: ['高血压', '糖尿病', '冠心病', '脑血管病', '肾病', '慢性肝炎', '吸烟', '酗酒', '未初潮', '已绝经', '花粉过敏', '霉菌过敏'],
    jtcy: ['父亲', '母亲', '兄弟姐妹', '子女'],
    jzs: ['高血压', '糖尿病', '冠心病', '脑中风', '肺癌', '乳腺癌(女)', '其他心血管疾病'],
    grs: ['吸烟', '偶尔吸烟', '长期吸烟', '不饮酒', '偶尔饮酒', '长期饮酒', '未婚', '已婚', '未孕', '闭经', '有早产史', '有流产史', '有痛经史'],
    textValue: '',
    type: null //1.过敏史 2.既往史 3.家族史 4.个人史
  },
  ready: function() {
    this.setData({
      allergy: this.properties.allergy,
      always: this.properties.always,
      family: this.properties.family,
      personal: this.properties.personal
    })
  },
  /**
   * 组件的方法列表
   */
  methods: {

    // 单选按钮选择
    radioChange(e) {
      var type = e.currentTarget.dataset.type
      var flag = e.detail.value == 1 ? false : true
      var model = type == 1 ? 'allergy.checked' : type == 2 ? 'always.checked' : type == 3 ? 'family.checked' : 'personal.checked'
      this.setData({
        type: type,
        showPicker: flag,
        [model]: flag,
        textValue: type == 1 ? this.data.allergy.content : type == 2 ? this.data.always.content : type == 3 ? this.data.family.content : this.data.personal.content
      })
      var data = {
        allergy: this.data.allergy,
        always: this.data.always,
        family: this.data.family,
        personal: this.data.personal
      }
      this.triggerEvent('propContent', { data }, {})
    },
    // 内容简介点击
    addcontent(e) {
      var type = e.currentTarget.dataset.id
      this.setData({
        showPicker: true,
        type: type,
        textValue: type == 1 ? this.data.allergy.content : type == 2 ? this.data.always.content : type == 3 ? this.data.family.content : this.data.personal.content
      })
    },
    // 关闭方法
    close() {
    //  var data = this.data.type=='1'?'allergy.content':'always.content'
      this.setData({
        showPicker: false
      })
      if (this.data.allergy.content == '') {
        this.setData({
          ['allergy.checked']: false
        })
      }
      if (this.data.always.content == '') {
        this.setData({
          ['always.checked']: false
        })
      }
      if (this.data.family.content == '') {
        this.setData({
          ['family.checked']: false
        })
      }
      if (this.data.personal.content == '') {
        this.setData({
          ['personal.checked']: false
        })
      }
      var data = {
        allergy: this.data.allergy,
        always: this.data.always,
        family: this.data.family,
        personal: this.data.personal
      }
      this.triggerEvent('propContent', { data }, {})
    },
    textContFun(e) {
      if (e.detail.keyCode === 10 && e.detail.value.length === 1) {
        this.setData({
          textValue: '',
          [model]: ''
        })
        return false
      }
      // var model = this.data.type == 1 ? 'allergy.content' : 'always.content'
      var model = this.data.type == 1 ? 'allergy.checked' : this.data.type == 2 ? 'always.checked' : this.data.type == 3 ? 'family.checked' : 'personal.checked'
      console.log(util.filterEmoji(e.detail.value), 110)
      this.setData({
        textValue: util.filterEmoji(e.detail.value) ? util.filterEmoji(e.detail.value) : '',
        [model]: util.filterEmoji(e.detail.value)
      })
    },
    clearValue() {
      this.setData({
        textValue: ''
      })
    },
    confirFun(e) {
      var content = this.data.textValue
      // var model = this.data.type == 1 ? 'allergy.content' : 'always.content'
      var model = this.data.type == 1 ? 'allergy.content' : this.data.type == 2 ? 'always.content' : this.data.type == 3 ? 'family.content' : 'personal.content'
      // var modelCheck = this.data.type == 1 ? 'allergy.checked' : 'always.checked'
      var modelCheck = this.data.type == 1 ? 'allergy.checked' : this.data.type == 2 ? 'always.checked' : this.data.type == 3 ? 'family.checked' : 'personal.checked'
      if (!content) {
        if (this.data.type == 1) {
          this.setData({
            'allergy.checked': false
          })
          wx.showToast({
            title: '请输入您的过敏史~',
            icon: 'none'
          })
        } else if (this.data.type == 2) {
          this.setData({
            'always.checked': false
          })
          wx.showToast({
            title: '请输入您的既往史~',
            icon: 'none'
          })
        } else if (this.data.type == 3) {
          this.setData({
            'family.checked': false
          })
          wx.showToast({
            title: '请输入您的家族史~',
            icon: 'none'
          })
        } else {
          this.setData({
            'personal.checked': false
          })
          wx.showToast({
            title: '请输入您的个人史~',
            icon: 'none'
          })
        }
        // return false
      } else {
        this.setData({
          [model]: content,
          [modelCheck]: true,
          showPicker: false
        })
        var data = {
          allergy: this.data.allergy,
          always: this.data.always,
          family: this.data.family,
          personal: this.data.personal
        }
        console.log(data, 148)
        this.triggerEvent('propContent', { data }, {})
      }

    },
    tagChoose(e) {
      var value = e.currentTarget.dataset.value
      var textValue = this.data.textValue
      if (textValue.length + value.length < 1000) {
        this.setData({
          textValue: textValue == '' ? value : textValue + ',' + value
        })
      } else {
        util.showToast({ title: '最多输入1000字' })
      }

    }
  }
})