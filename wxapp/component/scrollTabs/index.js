// component/scrollTabs/index.js
const util = require('../../utils/util')
const api = require('../../config/api')
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    /**
     * 黏贴定位
     * */
    sticky: {
      type: Boolean,
      value: false
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    scrollLeft: 0,
    currentTab: 0,
    scrollIntoView: '',
    navArr: []
  },
  lifetimes: {
    ready: function() {
      this.getList()
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    // 获取元素位置
    handleScroll(selectedId) {
      var that = this
      // 自定义组件内部使用 wx.createSelectorQuery() 需要加 in(this)
      var query = wx.createSelectorQuery().in(that) //创建节点查询器
      query.select('#item-' + selectedId).boundingClientRect() //选择id='#item-' + selectedId的节点，获取节点位置信息的查询请求
      query.select('#scroll-view').boundingClientRect() //获取滑块的位置信息
      //获取滚动位置
      query.select('#scroll-view').scrollOffset() //获取页面滑动位置的查询请求
      query.exec(function(res) {
        console.log('res:', res)
        that.setData({
          scrollLeft:
            res[2].scrollLeft +
            res[0].left +
            res[0].width / 2 -
            res[1].width / 2
        })
      })
    },
    // 导航tab切换
    swatchTab(e) {
      const { index, id } = e.currentTarget.dataset
      this.handleScroll(index)
      this.setData({
        currentTab: index
      })
      this.triggerEvent('onSwatchTab', { id, index })
    },
    async getList() {
      try {
        const { data } = await util.request(api.groupsList, {}, 'get', 1, false)
        if (data.code !== 0) {
          throw new Error(data.msg)
        }
        this.setData({
          navArr: data.data
        })
        if(data.data.length){
          this.triggerEvent('onSwatchTab', { id: data.data[0].id })
        }
      } catch (error) {
        util.showToast({
          title: error.message,
          icon: 'none',
          duration: 3000
        })
      }
    }
  }
})
