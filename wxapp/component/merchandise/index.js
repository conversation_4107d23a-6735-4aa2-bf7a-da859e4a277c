var api = require('../../config/api.js')
const util = require('../../utils/util')
import Toast from '../../lib/vant-weapp/toast/toast'

Component({
  properties: {
    size: {
      type: String,
      value: ''
    },
    showCar: {
      type: Boolean,
      value: true
    },
    horizontal: {
      type: Boolean,
      value: false
    },
    subTitle: {
      type: Boolean,
      value: false
    },
    showStepper: {
      type: Boolean,
      value: false
    },
    detail: {
      type: Object,
      observer(val) {
        this.setData({
          number: val.quantity
        })
      }
    },
    customTitle: {
      type: String,
      value: ''
    },
    otcConsultBuying: {
      type: Boolean,
      value: true
    },
    allClassifyHorizontal: {
      type: Boolean,
      value: false
    }
  },
  data: {
    caricon: api.ImgUrl + 'images/<EMAIL>',
    number: 0
  },
  methods: {
    goDetail: util.throttle((e) => {
      wx.navigateTo({
        url: '/pages/goods/goods?skuId=' + e.currentTarget.dataset.detail.skuId
      })
    }, 300),
    stepperChange(e) {
      this.setData({
        number: e.detail
      })
      this.triggerEvent('change', {
        number: e.detail,
        goods: this.properties.detail
      })
    },
    addCart() {
      if (this.loading || this.properties.remainQuantity === 0 || this.properties.stockStatus === 0) {
        return
      }
      this.loading = true
      const url = api.addCarts + `?skuId=${this.properties.detail.skuId}&quantity=1`
      util.request(url, {}, 'post').then(res => {
        this.loading = false
        if (res.data.code === 0) {
          Toast({
            position: 'bottom',
            message: '加购成功',
            context: this
          })
          this.triggerEvent('addCart')
        }
      })
    }
  }
})
