.merchandise {
  background: #fff;
  border-radius: 8rpx;
  overflow: hidden;
}

.merchandise.mid .image-wrap {
  flex-shrink: 0;
  margin: 16rpx;
  width: 180rpx;
  height: 180rpx;
  overflow: hidden;
  border-radius: 8rpx;
}

.merchandise .image-wrap {
  flex-shrink: 0;
  margin: 48rpx;
  width: 248rpx;
  height: 248rpx;
  position: relative;
}

.merchandise image {
  width: 100%;
  height: 100%;
}

.merchandise.mid .title {
  margin: 0 10rpx;
}

.merchandise .title {
  margin: 20rpx;
  font-size: 28rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 40rpx;
}
.merchandise.mid .price-wrap {
  font-size: 28rpx;
  padding: 0 10rpx 10rpx 10rpx;
}

.merchandise .price-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx 20rpx 20rpx;
  font-size: 32rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #f05542;
  height: 65rpx;
  /* line-height: 44rpx; */
}
.merchandise .price-wrap .caricon {
  width: 48rpx;
  height: 48rpx;
}

.cart-icon {
  height: 48rpx;
}

.horizontal {
  display: flex;
}

.horizontal.merchandise .title {
  margin: 0;
}

.horizontal.merchandise .image-wrap {
  flex-shrink: 0;
  margin: 40rpx;
  width: 132rpx;
  height: 132rpx;
}

.horizontal.merchandise .price-wrap {
  display: flex;
  justify-content: space-between;
  padding: 0;
}

.horizontal.merchandise .merchandise-content {
  width: 0;
}

.horizontal.merchandise .ellipsis2 {
  height: auto;
}

.sub-title {
  font-size: 24rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #999999;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.sub-title-red {
  font-size: 22rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #f05542;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.merchandise-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  margin-right: 20rpx;
}
.ellipsis2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 80rpx;
}

.ellipsis2 .label {
  border: 1px solid #f05542;
  color: #f05542;
  font-size: 18rpx;
  margin-right: 10rpx;
  border-radius: 5rpx;
  vertical-align: bottom;
}

.image-wrap .cover {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background: rgba(0, 0, 0, 0.6);
  font-size: 28rpx;
}

.layer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  background: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
  z-index: 99;
}

.all-horizontal {
  display: flex;
  justify-content: space-between;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 20rpx;
  height: 173rpx;
}

.all-horizontal.merchandise {
  border-radius: 0;
}

.all-horizontal.merchandise .title {
  margin: 0;
}

.all-horizontal.merchandise .image-wrap {
  flex-shrink: 0;
  width: 152rpx;
  height: 152rpx;
  border-radius: 8rpx;
  border: 1rpx solid #eeeeee;
  margin: 0;
}

.all-horizontal.merchandise .image-wrap .cover {
  border-radius: 8rpx;
}

.all-horizontal.merchandise .price-wrap {
  display: flex;
  justify-content: space-between;
  padding: 0;
}

.all-horizontal.merchandise .merchandise-content {
  width: 0;
  margin-left: 20rpx;
  margin-right: 0;
}

.all-horizontal.merchandise .ellipsis2 {
  height: auto;
}
