<view class="merchandise {{size}} {{horizontal ? 'horizontal' : ''}} {{allClassifyHorizontal? 'all-horizontal' : ''}}" bind:tap="goDetail" data-detail="{{detail}}">
  <view class="image-wrap">
    <image mode="aspectFit" src="{{detail.thumb || detail.icon}}"></image>
    <view wx:if="{{detail.rx === 1 && (detail.status !== 0 && detail.remainQuantity !== 0 && !(detail.quantity && detail.quantity > detail.remainQuantity))}}" class="layer">
      <view wx:if="{{customTitle}}" style="text-align: center;">
        <text>{{customTitle}}</text>
      </view>
      <view wx:else style="text-align: center;">
          <view>处方药</view>
          <view>凭处方在医师</view>
          <view>指导下购买使用</view>
      </view>
    </view>
    <view wx:if="{{detail.status === 0}}" class="cover">
      已下架
    </view>
    <view wx:if="{{detail.remainQuantity === 0}}" class="cover">
      无货
    </view>
    <view wx:if="{{detail.quantity > detail.remainQuantity}}" class="cover">
      库存不足
    </view>
  </view>
  <view class="merchandise-content">
    <view class="title">
      <view class="ellipsis2"><text class="label" wx:if="{{detail.rx === 1}}">处方药</text><text>{{detail.name || detail.fullName}}</text></view>
      <view wx:if="{{subTitle}}" class="sub-title">{{detail.indications}}</view>
      <view wx:if="{{detail.quantity > detail.remainQuantity}}" class="sub-title-red">库存仅剩{{detail.remainQuantity}}件</view>
    </view>
    <view class="price-wrap">
      <view class="cart-icon">¥{{detail.displayPrice || detail.salePrice}}</view>
      <van-stepper wx:if="{{showStepper}}" value="{{ number }}" min="{{1}}" max="{{detail.quantity > detail.remainQuantity ? detail.quantity : detail.remainQuantity}}" bind:change="stepperChange" catch:tap></van-stepper>
      <view wx:if="{{detail.rx === 2 && !otcConsultBuying}}" class="cart-icon" catch:tap>
        <image wx:if="{{showCar}}" class="caricon" src="{{caricon}}" bind:tap="addCart"></image>
      </view>
      </view>
  </view>
</view>
<van-toast id="van-toast" />
