const api = require('../../config/api')
const util = require('../../utils/util')
Component({
  properties: {
  },
  data: {
    ic_suspension_car: api.ImgUrl + 'images/<EMAIL>',
    number: 0
  },
  lifetimes: {
    attached: function() {

    },
    detached: function() {
    }
  },
  pageLifetimes: {
    show: function() {
      const token = wx.getStorageSync('token')
      if (!token) return
      this.getNumber()
    }
  },
  methods: {
    goShoppingCart() {
      wx.navigateTo({
        url: '/pages/shoppingCart/index'
      })
    },
    setNumber(number) {
      this.setData({
        number
      })
    },
    getNumber() {
      util.request(api.cartsInfo).then(res => {
        const list = res.data.data.groups[0] ? res.data.data.groups[0].items : []
        let number = 0
        list.forEach(item => {
          number += item.quantity
        })
        this.setNumber(number)
      })
    }
  }
})
