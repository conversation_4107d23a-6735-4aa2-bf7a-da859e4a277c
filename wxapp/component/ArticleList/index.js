const util = require('../../utils/util')
const api = require('../../config/api')
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    list: Array
  },

  /**
   * 组件的初始数据
   */
  data: {
    imgObject: {
      nomes: api.ImgUrl + 'images/nomes.png'
    }
  },
  lifetimes: {
  },
  /**
   * 组件的方法列表
   */
  methods: {
    goDetail(e) {
      const { id } = e.currentTarget.dataset
      wx.navigateTo({
        url: `/pages/article/articleDetail/index?id=${id}&type=article`
      })
    }
  }
})
