let api = require('../../config/api.js')
const util = require('../../utils/util')
import Toast from '../../lib/vant-weapp/toast/toast'
/**
 * 瀑布流组件
 * @param {Array} medicines 药品列表
 */
Component({
  properties: {
    medicines: {
      type: Array,
      defaultValue: []
    }
  },
  data: {
    caricon: api.ImgUrl + 'images/<EMAIL>',
    dataList: [],
    columnData: []
  },
  observers: {
    medicines(val) {
      this.setMedicines()
    }
  },
  methods: {
    setMedicines() {
      this.data.dataList = JSON.parse(JSON.stringify(this.properties.medicines))
      var resultArray = this.data.dataList.reduce(
        (acc, cur, index) => {
          const targetIndex = index % 2
          cur.id = index
          acc[targetIndex].push(cur)
          return acc
        },
        Array.from(Array(2), () => [])
      )
      this.setData({ columnData: resultArray })
      console.log(this.data.columnData, 'this.data.columnData')
    },
    addCart(e) {
      const { remainQuantity, stockStatus, skuId } = e.currentTarget.dataset.detail
      if (this.loading || remainQuantity === 0 || stockStatus === 0) {
        return
      }
      this.loading = true
      const url = api.addCarts + `?skuId=${skuId}&quantity=1`
      util.request(url, {}, 'post').then((res) => {
        this.loading = false
        if (res.data.code === 0) {
          Toast({
            position: 'bottom',
            message: '加购成功',
            context: this
          })
          this.triggerEvent('addCart')
        }
      })
    },
    goDetail: util.throttle((e) => {
      wx.navigateTo({
        url: '/pages/goods/goods?skuId=' + e.currentTarget.dataset.detail.skuId
      })
    }, 300)
  }
})
