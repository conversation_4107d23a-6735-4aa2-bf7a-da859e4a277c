<view class="waterfall">
    <view wx:for="{{columnData}}" wx:for-item="columnItem" wx:key="index" class="column">
        <block wx:for="{{columnItem}}" wx:key="list" style="width: 100%">
            <view class="content-item" bind:tap="goDetail" data-detail="{{item}}">
                <view class="layer-box">
                    <image src="{{item.thumb || item.icon}}" class="content-item-img" mode="widthFix" lazy-load="{{true}}" />
                    <view wx:if="{{item.rx === 1 && (item.status !== 0 && item.remainQuantity !== 0 && !(item.quantity && item.quantity > item.remainQuantity))}}" class="layer">
                        <view style="text-align: center;">
                            <view>处方药</view>
                            <view>凭处方在医师</view>
                            <view>指导下购买使用</view>
                        </view>
                    </view>
                    <view wx:if="{{item.status === 0}}" class="cover">已下架</view>
                    <view wx:if="{{item.remainQuantity === 0}}" class="cover">无货</view>
                    <view wx:if="{{item.quantity > item.remainQuantity}}" class="cover">库存不足</view>
                </view>
                <view class="content-item-box">
                    <view class="content-item-title">
                        <text class="label" wx:if="{{item.rx === 1}}">处方药</text>
                        <text>{{item.name || item.fullName}}</text>
                    </view>
                    <view class="price-wrap">
                        <view class="cart-icon">¥{{item.salePrice}}</view>
                        <view wx:if="{{item.rx === 2}}" class="cart-icon" catch:tap>
                            <image class="caricon" src="{{caricon}}" data-detail="{{item}}" bind:tap="addCart"></image>
                        </view>
                    </view>
                </view>
            </view>
        </block>
    </view>
</view>