.waterfall {
  display: flex;
  justify-content: space-around;
  margin: 0 10rpx;
}
.column {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 48%;
}
.content-left,
.content-right {
  width: 48%;
}
.content-item {
  margin: 10rpx auto 30rpx;
  background: #fff;
  width: 98%;
  border-radius: 20rpx;
  overflow: hidden;
}
.content-item-img {
  width: 100%;
}
.content-item-box {
  width: 95%;
  margin-left: 2.5%;
  font-size: 24rpx;
  color: #333;
  padding: 5rpx;
  box-sizing: border-box;
}
.content-item-title {
  /* 2行省略 */
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  font-size: 28rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 40rpx;
}

.label {
  border: 1px solid #f05542;
  color: #f05542;
  font-size: 18rpx;
  margin-right: 10rpx;
  border-radius: 5rpx;
  vertical-align: bottom;
}

.caricon {
  width: 48rpx;
  height: 48rpx;
}

.price-wrap {
  display: flex;
  justify-content: space-between;
  padding-top: 15rpx;
  padding-bottom: 10rpx;
  font-size: 32rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #f05542;
  line-height: 44rpx;
}

.content-item-name {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20rpx;
  font-size: 22rpx;
}

.layer-box {
  position: relative;
}

.layer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  background: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
  z-index: 999;
}

.cover {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background: rgba(0, 0, 0, 0.6);
  font-size: 28rpx;
  z-index: 999;
}
