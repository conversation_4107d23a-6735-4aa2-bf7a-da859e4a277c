//index.js
var api = require('../../../config/api.js')
var util = require('../../../utils/util')
var Config = require('../../../config/index.js')
Page({
  /**
   * 页面的初始数据
   */
  data: {
    id: '',
    detail: null,
    navTitle: '',
    finderUserName: Config.finderUserName,
    type: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.setData(
      {
        id: options.id,
        type: options.type || ''
      },
      () => {
        this.getData()
      }
    )
  },
  async getData() {
    const { id, type } = this.data
    try {
      const apiObj = {
        'banner': api.bannerDetail + `/${id}`,
        'zone': util.getRealUrl(api.zoneArticleDetail, id),
        'article': api.articleDetail + `/${id}`
      }
      const API = apiObj[type]
      const { data } = await util.request(API)
      if (data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
        return
      }
      //正则给img标签增加class
      data.data.content = data.data.content.replace(/\<img/gi, '<img class="rich-img" ')
      this.setData({
        detail: data.data
      })
    } catch (error) {
      throw new Error(error)
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {}
})