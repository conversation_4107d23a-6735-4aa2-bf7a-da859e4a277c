/* pages/famousDoctor/famousDoctor.wxss */
@import "../search/search.wxss";

.search{
  width: 100%;
}
.Select{
  position: relative;
  width: 100%;
  height: 84rpx;
  background: #fff;
}
.SelectHead{
  width: 100%;
  height: 84rpx;
  display: flex;
  justify-content: space-around;
}
.SelectHead view{
  /* width: 33.3%; */
  /* float: left; */
  text-align: center;
  line-height: 84rpx;
  overflow-x: hidden;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.SelectHead view text{
  font-size: 28rpx;
}
.SelectHead view  image{
  display: inline-block;
  width:40rpx;height: 40rpx;
  vertical-align: top;
  margin-top: 22rpx;
}

.gold_medal{
  width: 82rpx;
  height: 32rpx;
  position: absolute;
  left: 50%;
  margin-left: -41rpx;
  bottom: -10rpx;
}
.delet{
  position: absolute;
  width: 80rpx;
  height: 80rpx;
  right: 0rpx;
  top: 0rpx;
  z-index: 100;
}
.delet image{
  display: block;
  width: 40rpx;
  height: 40rpx;
  margin: 0 auto;
  margin-top: 20rpx;
}
.propMain{
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  background: rgba(0,0,0,0.6);
  z-index: 1;
}
.cur{
  color: var(--themeColor);
}
.SelectBox{
  width: 100%;
  position: absolute;
  left: 0;
  top:84rpx;
  background: #fff;
  height: 936rpx;
}
.sliderItem{
  height: 100%;
  padding-top: 14rpx;
  padding-bottom: 14rpx;
  width: 50%;
  box-sizing: border-box;
}
.sliderDiseaseItem {
  width: 100%!important;
}

