// pages/famousDoctor/famousDoctor.js
var api = require('../../config/api')
const util = require('../../utils/util')
const app = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    imgObject: {
      search: api.ImgUrl + 'images/<EMAIL>', //搜索图标
      del: api.ImgUrl + 'images/<EMAIL>', //删除
      dropdown: api.ImgUrl + 'images/ic_doctor_dropdown.png', //slider下
      dropUp: api.ImgUrl + 'images/ic_doctor_retract_blue.png', //slider上
      down_blue: api.ImgUrl + 'images/ic_doctor_down_blue.png', //slider蓝色向下箭头
      gold_medal: api.ImgUrl + 'images/ic_doctor_gold_medal.png' //金牌
    },
    keyWord: '',
    list: [],
    departmentslider: false, //科室选择弹窗控制
    cityslider: false, //地区选择弹窗控制
    departmentName: '全部科室', //科室最终选择汉字
    citysName: '全国', //地区最终选择汉字
    departmentFlag: false, //科室选择高亮
    citysFlag: false, //地区选择高亮
    departmentChoose: '', //科室一级菜单下拉选择下标
    citysChoose: '', //地区一级菜单下拉选择下标
    citysId: '', //地区最终选择ID
    departmentId: '', //科室最终选择ID
    citys: [], //地区字典
    departments: [], //科室字典
    listQuery: {
      page: 1, // 页码
      labelId: 1
    },
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '医生列表',
    templateId: [],
    authShow: false,
    clickFlag: true,
    cityToView: 'cityElement',
    departToView: 'departElement',
    type: null, //1.图文 2.视频
    doctorName: '',
    doctorId: '',
    tapTime: '',
    diseaseSlider: false, //常见疾病选择弹窗控制
    diseaseDefaultName: '全部疾病',
    diseaseName: '',
    diseaseFlag: false, //常见疾病选择高亮
    diseaseId: '',
    commonDisease: [],//常见疾病字典
    isLabelId: '' //是否专家名医
  },
  
  // 咨询记录
  counselHistory(e) {
    var doctorId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/consult/record/index?doctorId=' + doctorId
    })
  },
  changeKeyWord(e) {
    this.setData({
      keyWord: util.filterEmoji(e.detail.value)
    })
  },
  changeSearch(e) {
    if (this.data.keyWord !== '') {
      this.geDoctortList(1)
    }
  },
  deleteKeyWord() {
    this.setData({
      keyWord: ''
    })
    this.geDoctortList(1)
  },
  SelectFun(e) {
    var type = e.currentTarget.dataset.id //1：科室选择  2:地区选择 3: 常见疾病选择
    const aFlag = this.data.departmentslider && !this.data.departmentId //科室高亮
    const bFlag = this.data.cityslider && !this.data.citysId //地区高亮
    const cFlag = this.data.diseaseSlider && !this.data.diseaseId //常见疾病高亮
    if (type === '1') {
      if (bFlag || cFlag) {
        console.log(111)
        this.setData({
          citysFlag: false,
          diseaseFlag: false
        })
      }
      this.setData({
        departmentFlag: !aFlag,
        departmentslider: this.data.departmentslider ? false : true,
        departmentName: !this.data.departmentId ? '全部科室' : this.data.departmentName,
        cityslider: false,
        diseaseSlider: false
      })
    } else if(type === '2') {
      if (aFlag || cFlag) {
        this.setData({
          departmentFlag: false,
          diseaseFlag: false
        })
      }
      this.setData({
        citysFlag: !bFlag,
        cityslider: this.data.cityslider ? false : true,
        citysName: !this.data.citysId ? '全国' : this.data.citysName,
        departmentslider: false,
        diseaseSlider: false
      })
    } else {
      // 常见疾病
      if(aFlag || bFlag) {
        this.setData({
          departmentFlag: false,
          citysFlag: false
        })
      }
      this.setData({
        diseaseFlag: !cFlag,
        diseaseSlider: this.data.diseaseSlider ? false : true,
        diseaseDefaultName: !this.data.diseaseId ? '全部疾病' : this.data.diseaseDefaultName,
        departmentslider: false,
        cityslider:false
      })
    }

  },
  ChooseFun(e) {
    const model = e.currentTarget.dataset.model //0科室  1地区  2常见疾病
    const type = e.currentTarget.dataset.type //第几级菜单
    const id = e.currentTarget.dataset.id //选中ID
    const value = e.currentTarget.dataset.value //当前选中name
    var index = e.currentTarget.dataset.index //下标
    if (model === '0') {
      // 当前点击的是子菜单获取参数
      if (type === '1') {
        this.setData({
          departmentName: value,
          departmentFlag: true,
          departmentslider: false,
          departmentId: id
        })
        this.geDoctortList(1)
      } else {
        // 点击一级菜单给高亮
        this.setData({
          departmentChoose: index,
          departToView: 'departElement'
        })
        if (id === '') {
          this.setData({
            departmentId: '',
            departmentFlag: false,
            departmentslider: false,
            departmentName: '全部科室'
          })
          this.geDoctortList(1)
        }
      }
    } else if(model === '1') {
      if (type === '1') {
        this.setData({
          citysName: value,
          citysFlag: true,
          cityslider: false,
          citysId: id
        })
        this.geDoctortList(1)
      } else {
        this.setData({
          citysChoose: index,
          cityToView: 'cityElement'
        })
        if (id === '') {
          this.setData({
            citysId: '',
            citysFlag: false,
            cityslider: false,
            citysName: '全国'
          })
          this.geDoctortList(1)
        }
      }
    } else {
      // 常见疾病
      this.setData({
        diseaseDefaultName: value,
        diseaseName: value,
        diseaseFlag: true,
        diseaseSlider: false,
        diseaseId: id
      })
      if(id === '') {
        this.setData({
          diseaseId: '',
          diseaseFlag: false,
          diseaseSlider: false,
          diseaseDefaultName: '全部疾病',
          diseaseName: ''
        })
      }
      this.geDoctortList(1)
    }
  },
  closeChoose(e) {
    if (this.data.departmentslider) {
      this.setData({
        departmentFlag: !(this.data.departmentFlag && !this.data.departmentId),
        departmentslider: false,
        departmentName: !this.data.departmentId ? '全部科室' : this.data.departmentName
      })
    }
    if (this.data.cityslider) {
      this.setData({
        citysFlag: !(this.data.citysFlag && !this.data.citysId),
        cityslider: false,
        citysName: !this.data.citysId ? '全国' : this.data.citysName
      })
    }
    // 常见疾病
    if(this.data.diseaseSlider) {
      this.setData({
        diseaseFlag: !(this.data.diseaseFlag && !this.data.diseaseId),
        diseaseSlider: false,
        diseaseDefaultName: !this.data.diseaseId ? '全部疾病' : this.data.diseaseDefaultName
      })
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log(options);
    this.setData({
      keyWord: options.keyword ? options.keyword : '',
      departmentId: options.departmentId || '',
      departmentName: options.departmentName || '全部科室',
      diseaseId: options.diseaseId || '',
      diseaseDefaultName: options.diseaseName || '全部疾病',
      diseaseName: options.diseaseName,
      isLabelId: options.labelId || '', // 2为我的医生
      // navTitle: +options.labelId === 2 ? '我的医生' : options.labelId ? '专家名医' : '医生列表' 
    })
    this.getCityList()
    this.fetchDiseaseList()
  },

  onReady() {
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    this.geDoctortList(1)
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    if (this.data.loadComplete) {
      this.setData({
        ['listQuery.page']: ++this.data.listQuery.page
      })
      this.geDoctortList(2)
    }

  },
  async getCityList() {

    var that = this
    const { data } = await util.request(api.cityList, {}, 'post', 1, false)
    if (data.code === 0) {
      const departArry = [{ id: '', name: '全部科室' }]
      const cityArry = [{ id: '', name: '全国' }]
      that.setData({
        citys: cityArry.concat(data.data.cites.list),
        departments: departArry.concat(data.data.departments.list)
      })
    } else {
      util.showToast({ title: data.msg })
    }
  },
  geDoctortList(type){
    if(this.data.isLabelId == 2){
      this.getMyDoctor(type)
    }else{
      this.getclinic(type)
    }
  },
  async getMyDoctor(type) {
    if (type !== 1 && type !== 2) {
      type = 1
    }
    var that = this
    try {
      const { data } = await util.request(
        api.myDoctor,
        that.data.listQuery,
        'post',
        '1',
        false
      )
      if (!data) {
        return false
      }
      const result = data.data
      console.log(result, '======data======')
      if (data.code === 0) {
        that.setData({
          list:
            type === 1 ? result.result : that.data.list.concat(result.result),
          loadComplete: result.hasNext
        })
      } else {
        util.showToast({
          icon: 'none',
          title: data.msg
        })
      }
      return true
    } catch (error) {
      return false
    }
  },
  async getclinic(type) { //type=1 直接请求 =2分页请求
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    var that = this
    var parmas = {
      page: type === 1 ? 1 : that.data.listQuery.page,
      departmentId: that.data.departmentId,
      cityId: app.globalData.cityId,
      keyword: that.data.keyWord,
      // labelId: that.data.isLabelId,
      diseasesName: that.data.diseaseName
    }
    const { data } = await util.request(api.clinic, parmas, 'post', 2, false)
    util.hideToast()
    const result = data.data
    if (data.code === 0) {
      that.setData({
        list: type === 1 ? result.result : that.data.list.concat(result.result),
        loadComplete: !result.hasNext ? false : true
      })
    } else {
      util.showToast({
        icon: 'none',
        title: data.msg
      })
    }
  },
  /**
   * 获取常见疾病
   */
  async fetchDiseaseList() {
    try {
      const { data } = await util.request(api.diseaseList,{ limit: 20 }, 'get', 1, false)
      if(data.code === 0) {
        const diseaseArr = [{ id: '', name: '全部疾病' }]
        this.setData({
          commonDisease: diseaseArr.concat(data.data)
        })
      } else {
        util.showToast({ title: data.msg })
      }
    } catch (error) {
      throw new Error(error)
    }
  }
})
