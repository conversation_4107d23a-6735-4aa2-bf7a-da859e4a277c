// pages/from/fromDetail/index.js
const api = require('../../../config/api.js')
const util = require('../../../utils/util.js')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: null,
    url: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    const id = options.id
    const formId = options.formId
    const finishStatus = options.finishStatus
    const { _o } = util.headerParams
    let url = api.WebViewUrl + `/#/fromList?id=${id}&formId=${formId}&origin=${_o}&type=1&token=${wx.getStorageSync('token')}`
    if(finishStatus == 1){
      url = api.WebViewUrl + `/#/fromListRes?id=${id}&formId=${formId}&origin=${_o}&type=1&token=${wx.getStorageSync('token')}`
    }
    this.setData({
      url, id
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {

  }
})
