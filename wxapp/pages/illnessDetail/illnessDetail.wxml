<navbar isBack="true" backgroundColor='#fff' navTitle="{{navTitle}}">
</navbar>
<view class="container" bindtouchmove="touchMove">
	<view class="w100 bg-color-white p30">
		<view class="f32 b c333">基本信息</view>
		<view class="pt20 c666 f28">姓名：{{info.patientName}}</view>
		<view class="pt20 c666 f28">性别：{{info.patientGender==0?'女':'男'}}</view>
		<view class="pt20 c666 f28">年龄：{{info.patientAgeStr}}</view>
	</view>
	<view class="line"></view>
	<view class="w100 bg-color-white p30">
		<view class="f32 b c333">病情信息</view>
		<view class="f28 c666 pt20">是否线下诊断：是</view>
		<view class="f28 c666 pt10">线下诊断：{{info.offlineDiagnosis}}</view>
		<!--<view class="f28 c666 pt10">病情描述：{{info.description}}</view>-->
		<view class="f28 c666 pt10" wx:if="{{info.symptom}}">是否发热：{{info.symptom.fever == 0 ? '无发热': info.symptom.fever == 1 ? '低热' : '高热'}}</view>
		<view class="f28 c666 pt10" wx:if="{{info.symptom}}">是否为孕妇：{{info.symptom.pregnant == 0 ? '否':'是'}}</view>
		<view class="f28 c666 pt10" wx:if="{{info.symptom}}">是否患有高血压：{{info.symptom.hypertension == 0 ? '否':'是'}}</view>
		<view class="f28 c666 pt10" wx:if="{{info.symptom}}">是否患有糖尿病：{{info.symptom.diabetes == 0 ? '否':'是'}}</view>
	</view>
	<block wx:if="{{info.offlineDiagnosisImgs.length>0}}">
		<view class="line"></view>
		<view class="w100 bg-color-white p30">
			<view class="f32 b c333">图片资料</view>
			<view class="picList clearfix">
				<view class="item" wx:for="{{info.offlineDiagnosisImgs}}" data-index="{{index}}" bindtap="previewImg">
					<image mode="aspectFill" src="{{item}}"></image>
				</view>
			</view>
		</view>
	</block>
	<view class="line"></view>
	<view class="w100 bg-color-white p30">
		<view class="f32 b c333">过敏史</view>
		<view class="pt20 c666 f28 pb30 bb1">{{info.allergy!='' && info.allergy!==null ? info.allergy:'无'}}</view>
		<view class="f32 b c333 pt30">既往史</view>
		<view class="pt20 c666 f28">{{info.pastHistory!='' && info.pastHistory!==null ?info.pastHistory:'无'}}</view>
	</view>
</view>