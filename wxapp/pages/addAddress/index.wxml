<navbar isBack="{{isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
<view class="container">
	<van-cell-group>
		<van-field model:value="{{ receiver }}" clearable bind:input='onReceiver' label="收货人姓名"
			placeholder="请输入收货人真实姓名" />
		<van-field model:value="{{ phone }}" bind:focus='onPhonefocus' bind:blur='onPhoneblur' maxlength='11' type='number'
			clearable label="收货人手机号" placeholder="请输入手机号" />
		<AddresPicker label='所在地区' areaValue='{{cityText}}' inputAlign='left' bind:onPickerConfim='onPickerConfim'>
		</AddresPicker>
		<van-field model:value="{{ addr }}" clearable label="详细地址" bind:input='onaddr' bind:blur='onaddrblur'
			placeholder="街道、楼牌号等详细地址" />
		<van-cell title="设为默认地址">
			<van-switch slot="right-icon" size='26px' active-color="#38BF87" inactive-color="#b4b4b4"
				checked="{{ defaultAddr }}" bind:change="onChange" />
		</van-cell>
	</van-cell-group>
	<view class="fixed-button flex_m bt1">
		<view class="bttom-btn" bindtap='saveAddress'>保存</view>
	</view>
</view>