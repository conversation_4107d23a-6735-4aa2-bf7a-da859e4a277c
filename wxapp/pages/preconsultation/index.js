// pages/preconsultation/index.js
var api = require('../../config/api.js')
var util = require('../../utils/util')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '诊前咨询',
    counselorList: [],
    counselorQuery: {
      page: 1 // 页码
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getcounselorList()
  },

   //获取咨询师列表
   async getcounselorList() {
    util.showLoading({
      title: 'loading',
      mask: true
    })
    try {
      const { counselorList, counselorQuery } = this.data
      const { data } = await util.request(api.counselorList, {
        ...counselor<PERSON><PERSON><PERSON>
      }, 'get', 1, false)
      if (data.code !== 0) {
        throw new Error(data.msg)
      }
      this.setData({
        counselorList:
        counselorQuery.page > 1
          ? this.data.counselorList.concat(data.data.result)
          : data.data.result
      })
      console.log(counselorList.concat(data.data.result), 291)
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
    util.hideLoading()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})