<view class="page-container">
    <navbar class="nav" isBack="{{ false }}" isHome="{{ true }}" backgroundColor="#fff" navTitle="代付详情"></navbar>
    <view class="bg-color-gray-light pay-detail over">
        <view>
            <view wx:if="{{orderInfo.status == 1}}" class="c666 f24 time flex align-center">
                支付剩余时间：<van-count-down time="{{ countdown }}" />
            </view>
            <view class="color-ff0 center">
                <text class="f40 font-b mr12">¥</text>
                <text class="f80 font-b">{{orderInfo.realPay}}</text>
            </view>
            <view class="c999 f24 tip center">温馨提示：若未来订单申请退款，退款资金将原路退还代付人</view>
            <view class="bg-color-white br20">
                <view class="c333 f32 header">hi，我挑了个商品，帮我代付款一下吧～</view>
                <view class="line"></view>
                <view wx:for="{{productsArr}}" wx:key="{{item.skuId}}" class="p28 flex">
                    <view class="img">
                        <image src="{{item.icon}}" mode="aspectFit" />
                    </view>
                    <view class="flex flex-col ml20 p6">
                        <view class="c333 f28 font-b">{{item.name}}</view>
                        <view class="flex justify-between align-center">
                            <view class="c333 f28">¥{{item.price}}</view>
                            <view class="c999 f24">数量：{{item.quantity}}</view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view wx:if="{{orderInfo.payUserInfo}}" class="bg-color-white br20 mt20 p28">
            <view class="flex justify-between align-center">
                <view class="f28 c999">代付人：</view>
                <view class="f28 c333">{{orderInfo.payUserInfo.payUser}}</view>
            </view>
            <view class="line-l"></view>
            <view class="flex justify-between align-center">
                <view class="f28 c999">代付时间：</view>
                <view class="f28 c333">{{orderInfo.payUserInfo.payTime}}</view>
            </view>
        </view>
    </view>
    <view class="bg-color-white h198 btn-warp">
        <block wx:if="{{orderInfo.status == 1}}">
            <view class="m20-28 flex" wx:if="{{orderInfo.selfFlag}}">
                <button class="share w40" bindtap="goOrderDetail">订单详情</button>
                <button open-type="share" class="share flex-r-1">发给微信好友</button>
            </view>
            <view wx:else bindtap="orderPay" class="m20-28 share">立即支付</view>
        </block>
        <block wx:elif="{{orderInfo.status == 8}}">
            <view class="m20-28 share cf7f7f7">已取消</view>
        </block>
        <block wx:else>
            <view class="m20-28 share cf7f7f7">已支付</view>
        </block>
    </view>
</view>