.page-container {
  height: 100%;
}
.over {
  overflow: hidden;
}
.pay-detail {
  height: inherit;
}
.center {
  text-align: center;
}
.time {
  margin: 46rpx auto 20rpx;
  justify-content: center;
}
.color-ff0 {
  color: #ff0000;
}
.font-b {
  font-weight: bold;
}
.f80 {
  font-size: 80rpx;
}
.tip {
  margin: 20rpx auto 40rpx;
}
.mt20 {
  margin-top: 20rpx;
}
.mt28 {
  margin-top: 28rpx;
}
.mr12 {
  margin-right: 12rpx;
}
.br20 {
  border-radius: 20rpx;
}
.header {
  padding: 28rpx;
}
.line {
  height: 1rpx;
  background: #eeeeee;
  margin: 0 28rpx;
}
.line-l {
  height: 1rpx;
  background: #eeeeee;
  margin: 28rpx 0;
}
.h96 {
  height: 96rpx;
}
.m28 {
  margin: 28rpx;
}
.img {
  width: 156rpx;
  height: 156rpx;
  background: #ffffff;
  border-radius: 10rpx;
  border: 2rpx solid #eeeeee;
  display: flex;
  align-items: center;
  justify-content: center;
}
.img image {
  width: 100rpx;
  height: 100rpx;
}
.flex-col {
  flex-direction: column;
  justify-content: space-between;
}
.justify-between {
  justify-content: space-between;
}
.align-center {
  align-items: center;
}
.ml20 {
  margin-left: 20rpx;
}
.p28 {
  padding: 28rpx;
}
.m20-28 {
  margin: 20rpx 28rpx;
}
.share {
  height: 88rpx;
  background: #367dff;
  border-radius: 8rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 32rpx;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-warp {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
.h198 {
  height: 198rpx;
}
.mtb28 {
  margin: 28rpx 0;
}
.cf7f7f7 {
  background: #f7f7f7;
  color: #666 !important;
}
.w40 {
  width: 240rpx;
  margin-right: 20rpx;
}
.flex-r-1 {
  flex: 1;
}
.p6 {
  padding: 6rpx 0;
}
