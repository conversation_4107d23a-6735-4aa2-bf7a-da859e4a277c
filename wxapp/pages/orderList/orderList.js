// pages/orderList/orderList.js
const api = require('../../config/api')
const util = require('../../utils/util')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    activeColor: util.THEMECOLOR,
    navTitle: '我的订单',
    listQuery: {
      status: '0',
      page: 1,
      num: 10
    },
    orderList: [],
    statusBarHeight: null,
    templateId: [],
    orderStatus: [
      { id: 0, name: '全部' },
      { id: 1, name: '待支付' },
      { id: 2, name: '待发货' },
      { id: 3, name: '待收货' },
      { id: 4, name: '已结束' }
    ],
    show: false,
    actions: [
      {
        name: '在线客服',
        openType: 'contact'
      },
      {
        name: '电话客服'
      }
    ],
    platformServiceHotline: ''
  },
  authSub(orderNumber) {
    var that = this
    wx.requestSubscribeMessage({
      tmplIds: that.data.templateId,
      success: (res) => {
        that.paySuccess(orderNumber)
      },
      fail(res) {
        that.paySuccess(orderNumber)
      }
    })
  },
  // 获取模板
  async getTemplate() {
    var templateId = await util.getTemplate(3)
    this.setData({
      templateId: templateId
    })
  },
  tabClick(event) {
    this.setData({
      'listQuery.page': 1,
      'listQuery.status': event.currentTarget.dataset.id == 0 ? '' : event.currentTarget.dataset.id
    })
    if (wx.pageScrollTo) {
      wx.pageScrollTo({
        scrollTop: 0
      })
    }
    this.getList()
  },
  handleTap(e) {
    console.log(e, 'e')
    if (e.detail.drugType == 2) {
      wx.navigateTo({
        url: '/pages/orderTcmDetail/index?orderId=' + e.detail.orderId
      })
    } else {
      wx.navigateTo({
        url: '/pages/orderDetail/orderDetail?orderId=' + e.detail.orderId
      })
    }
  },
  // 查看物流
  handleLook(e) {
    var orderId = e.detail.orderId
    var orderSn = e.detail.orderSn
    wx.navigateTo({
      url: '/pages/logistics/logistics?orderId=' + orderId + '&orderSn=' + orderSn
    })
  },
  async handlePay(e) {
    util.showLoading({
      title: '请稍后',
      mask: true
    })
    try {
      const { data } = await util.request(util.getRealUrl(api.orderPay, e.detail.orderId))
      if (data.code !== 0) {
        throw new Error(data.msg)
      }
      wx.requestPayment({
        ...data.data,
        success: (result) => {
          this.authSub(e.detail.orderId)
        },
        fail: () => {}
      })
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
    setTimeout(() => {
      util.hideLoading()
    }, 2000)
  },
  paySuccess(orderNumber) {
    wx.navigateTo({
      url: '/pages/paySuccess/paySuccess?id=' + orderNumber
    })
  },
  handleCancel(e) {
    util.showModal({
      title: '',
      content: `取消订单后，再次购买需要咨询医生申请再次开方，确定取消吗？`,
      showCancel: true,
      cancelText: '我点错了',
      cancelColor: '#666666',
      confirmText: '确定',
      success: (result) => {
        if (result.confirm) {
          this.cancelOrder(e.detail)
        }
      }
    })
  },
  async cancelOrder(detail) {
    try {
      const { data } = await util.request(util.getRealUrl(api.orderCancel, detail.orderId))
      util.showToast({
        title: data.msg,
        icon: 'none',
        duration: 3000
      })
      if (data.code === 0) {
        setTimeout(() => {
          this.setData({
            'listQuery.page': 1
          })
          this.getList()
        }, 350)
      }
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
  },
  async getList() {
    util.showLoading({
      title: 'loading',
      mask: true
    })
    try {
      const { listQuery, orderList } = this.data
      console.log(listQuery.status);
      // listQuery 接口不支持status = 0
      const { data } = await util.request(api.orderList, { ...listQuery, status: listQuery.status === '0' ? null : listQuery.status })
      if (data.code !== 0) {
        throw new Error(data.msg)
      }
      this.setData({
        orderList: listQuery.page > 1 ? orderList.concat(data.data.result) : data.data.result,
        loadComplete: !data.data.hasNext ? false : true
      })
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
    util.hideLoading()
  },
  handleInvoice(e) {
    const url = e.detail.invoiceUrl
    const suffixType = url.substring(url.lastIndexOf(".") + 1)
    if (suffixType == 'jpg' || suffixType == 'png') {
      wx.previewImage({
        current: url, // 当前显示图片的http链接
        urls: [url] // 需要预览的图片http链接列表
      })
    } else {
      const fileName = 'pdf文件'
      const fileType = 'pdf'
      const randfile = new Date().getTime() + fileName
      const newPath = `${wx.env.USER_DATA_PATH}/${randfile}`
      wx.showLoading({ title: '下载中' })
      wx.downloadFile({
        url: url,
        filePath: newPath,
        success(res) {
          if (res.statusCode === 200) {
            let filePath = res.filePath
            console.log(filePath, 'filePath')
            wx.openDocument({ // 预览文件
              filePath: filePath,
              fileType: fileType,
              showMenu: true,
              success() {},
              fail(error) {
                console.log(error)
              }
            })
          }
        },
        fail() {
          wx.showToast({
            title: '文件预览失败',
            icon: 'error'
          })
        },
        complete() {
          wx.hideLoading()
        }
      })
    }
  },
  handleContact(event) {
    console.log(event)
    const { serviceHotline, platformServiceHotline } = event.detail
    if (serviceHotline) {
      wx.makePhoneCall({
        phoneNumber: serviceHotline
      })
    } else {
      this.setData({ show: true, platformServiceHotline })
    }
  },
  onSelect(event) {
    if (event.detail.name == '电话客服') {
      wx.makePhoneCall({
        phoneNumber: this.data.platformServiceHotline
      })
    }
  },
  onClose() {
    this.setData({ show: false })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.setData({
      ['listQuery.status']: options.type || '0',
      statusBarHeight: app.globalData.statusBarHeight
    })
    this.getTemplate()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    this.setData({
      ['listQuery.page']: 1
    })
    this.getList()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

    if (this.data.loadComplete) {
      this.setData({
        ['listQuery.page']: ++this.data.listQuery.page

      })
      this.getList()
    }
  }

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage: function() {

  // }
})
