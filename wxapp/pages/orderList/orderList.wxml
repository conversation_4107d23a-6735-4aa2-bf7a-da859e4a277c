<!-- pages/orderList/orderList.wxml -->
<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
<view class="pb20">
   <view class="tab flex" style="top:{{ 44 + statusBarHeight}}px;">
      <view wx:for="{{orderStatus}}" class="flex1 flex_c_m" wx:key="index">
         <text class=" {{listQuery.status==item.id?'cur':''}}" bindtap="tabClick" data-id="{{item.id}}">{{item.name}}</text>
      </view>
   </view>
   <view style="padding-top:84rpx">
       <orderCell
         orderList="{{orderList}}"
         bind:check="handleTap"
         bind:pay="handlePay"
         bind:cancel="handleCancel"
         bind:look="handleLook"
         bind:invoice="handleInvoice"
         bind:contact="handleContact"
       />
   </view>
</view>
<van-action-sheet
  show="{{ show }}"
  actions="{{ actions }}"
  safe-area-inset-bottom="{{true}}"
  safeAreaInsetBottom="{{true}}"
  title="工作时间：10:00-19:00"
  cancelText="取消"
  bind:close="onClose"
  bind:select="onSelect"
  bind:cancel="onClose"
/>
