// pages/home/<USER>/homeCity.js
const { cityData } = require('../../utils/city.js')
var api = require('../../config/api.js')
const app = getApp()
Page({
 
  /**
   * 页面的初始数据
   */
  data: {
    navTitle: '选择城市',
    current_city: "",
    search: "",
    imgObject: {
        search: api.ImgUrl + 'images/<EMAIL>' //搜索图标
    },
    hot_city: [
      {
        name: "北京市",
        id: 33
      }, {
        name: "上海市",
        id: 105
      }, {
        name: "广州市",
        id: 229
      }, {
        name: "深圳市",
        id: 231
      }, {
        name: "成都市",
        id: 271
      }, {
        name: "重庆市",
        id: 269
      }, {
        name: "天津市",
        id: 34
      },{
        name: "西安市",
        id: 324
      }, {
        name: "郑州市",
        id: 183
      },{
        name: "杭州市",
        id: 119
      }
    ],
    city_list: [],
    barHeight: 0,
    curr: -1,
    scrollViewId: "",
    barTop: 0,
    showLetter: false,
    result: [],//搜索结果
  },
 
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      current_city: options.currentCity || "北京市",
      city_list: cityData
    })
    wx.getSystemInfo({
      success: (res) => {
        let winHeight = res.windowHeight
        let barHeight = winHeight - res.windowWidth / 750 * 300;
        this.setData({
          barHeight: barHeight,
          barTop: res.windowWidth / 750 * 180,
        })
      }
    })
  },
/**
   * 获取value值
   * @param {*} e 
   */
  getValue(e) {
    this.setData({
      search: e.detail.value
    }, () => {
      this.search(e.detail.value)
    })
  },
  /**
   * 搜索成功
   */
  search(e) {
    let result = [], { city_list } = this.data;
    city_list.forEach((item1) => {
      item1.data.forEach((item2) => {
        if (item2.keyword.indexOf(e.toLocaleUpperCase()) !== -1) {
          result.push({ name: item2.cityName, id: item2.id })
        }
      })
    })
    this.setData({
      result,
    })
  },
  /**
   * 清空验证码
   */
  clear_input() {
    this.setData({
      search: ""
    })
  },
  touch(e) {
    let pageY = e.touches[0].pageY
    let index = Math.floor((pageY - this.data.barTop) / (this.data.barHeight / 22))//向下取整
    let item = this.data.city_list[index]
    if (item) {
      this.setData({
        scrollViewId: item.letter,
        curr: index
      })
    }
  },
  touchStart(e) {
    this.setData({
      showLetter: true
    })
    this.touch(e)
  },
  touchMove(e) {
    this.touch(e)
  },
  touchEnd() {
    this.setData({
      showLetter: false,
    })
  },
  touchCancel() {
    this.setData({
      showLetter: false,
    })
  },
//   changeCity(e){
//     console.log(e)
//     this.setData({
//         current_city: e.currentTarget.dataset.name,
//     })
//     this.clear_input()
//     app.globalData.city = e.currentTarget.dataset.name
//     wx.switchTab({
//         url: '/pages/home/<USER>'
//     })
//     console.log(1111)
//   },
  changeCity(e) {
    const currentCity = e.currentTarget.dataset.name
    this.clear_input()
    app.globalData.city = e.currentTarget.dataset.name
    app.globalData.cityId = e.currentTarget.dataset.id
    console.log('city', app.globalData.city)
    console.log('id', app.globalData.cityId)
    var pages = getCurrentPages()
    var prevPage = pages[pages.length - 2]
    console.log('prevPage', prevPage)
    console.log('currentCity1', app.globalData.city)
    prevPage.setData({
        currentCity: currentCity,
    })
    wx.navigateBack({
      delta: 1
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
 
  },
 
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
 
  },
 
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
 
  },
 
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
 
  },
 
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
 
  },
 
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
 
  },
 
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
 
  }
})