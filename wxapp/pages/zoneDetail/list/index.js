import Toast from '../../../lib/vant-weapp/toast/toast'
const api = require('../../../config/api.js')
const util = require('../../../utils/util')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '用药专区',
    list: [],
    page: 1,
    hasMore: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getZoneList()
  },

  // 获取专区列表
  async getZoneList() {
    try {
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
      })
      const response = await util.request(api.zoneList, { page: this.data.page,num: 10, orderBy: 'sort' })
      const { code, data, msg} = response.data
      if (code === 0) {
        Toast.clear()
        this.setData({
          list: this.data.page === 1 ? data.result : this.data.list.concat(data.result),
          hasMore: this.data.page < data.totalPages,
          page: this.data.page + 1
        })
      } else {
        Toast.fail(msg)
      }
    } catch (error) {
      throw new Error('请求失败：' + error)
    }
  },

  navTo(e) {
    const { id, title } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/zoneDetail/zoneDetail?id=${id}&title=${title}`
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({ 
      page: 1 
    }, () => {
      this.getZoneList()
      setTimeout(() => {
        wx.stopPullDownRefresh()
      }, 1000)
    })
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (!this.data.hasMore) {
      return
    }
    this.getZoneList()
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})