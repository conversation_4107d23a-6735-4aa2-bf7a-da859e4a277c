page {
  background: #f8f8f8;
}

.page-warp {
  background-color: #f8f8f8;
}

.flex {
  display: flex;
  justify-content: space-between;
}

.item {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin: 28rpx;
}

.warp {
  flex-basis: calc((100% - 22rpx) / 2);
  background: #ffffff;
  overflow: hidden;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
}

.warp .image {
  width: 100%;
  display: block;
  height: 232rpx !important;
}

.warp .text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 28rpx;
  color: #333333;
  padding: 20rpx;
}

.van-grid-item__content {
  padding: 0 !important;
}
