<view class="page-warp">
    <navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
    <!-- 搜索区域 -->
    <view class="good-search" id="search">
        <van-search
            value="{{value}}"
            shape="round"
            placeholder="搜索药品通用名/商品名称"
            use-action-slot
            use-left-icon-slot
            bind:change="onChange"
            bind:search="onSearch"
            bind:clear="onClear"
            >
            <image class="search-icon" slot="left-icon" src="{{ic_input_search}}" />
            <view class="flex" slot="action">
                <view class="cart" bind:tap="onCart">
                    <image class="cart-icon" style="width: 44rpx;height: 44rpx;" src="{{ic_cart}}" />
                    <view wx:if="{{cartNumber > 0}}" class="number">{{cartNumber}}</view>
                </view>
            </view>
        </van-search>
    </view>
    <view wx:if="{{showList}}" class="relevant" style="height: {{scrollViewHeight}}px;top: {{scrollViewTop}}px;">
        <view class="relevant-item" wx:for="{{relevantList}}" data-item="{{item}}" bind:tap="handleClick">
            <image class="search-icon" src="{{ic_input_search}}"></image>
            <text>{{ item.productName }}</text>
        </view>
    </view>
    <view wx:if="{{!showList}}">
        <!-- Banner -->
        <view class="banner" style="padding-top: {{searchHeight}}px" wx:if="{{banner}}" bindtap="toArticle">
            <image src="{{banner}}" mode="widthFix"></image>
        </view>
        <!-- 商品列表 -->
        <view class="{{ banner ? 'good-list' : 'pt-130' }}">
            <van-grid column-num="2" gutter="{{ 10 }}" border="{{false}}">
                <van-grid-item use-slot content-class="content-class" wx:for="{{featuredList}}" wx:key="index">
                    <merchandise detail="{{item}}" otcConsultBuying="{{otcConsultBuying}}" bind:addCart="handleaddCart"></merchandise>
                </van-grid-item>
            </van-grid>
        </view>
        <view class="load-more">{{hasMore ? '正在加载...' : ''}}</view>
        <nodata wx:if="{{featuredList.length === 0}}"></nodata>
        <van-toast id="van-toast" />
    </view>
</view>