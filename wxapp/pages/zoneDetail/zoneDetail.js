import Toast from '../../lib/vant-weapp/toast/toast'
const api = require('../../config/api.js')
const util = require('../../utils/util')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '心脑血管专区',
    ic_input_search: api.ImgUrl + 'images/<EMAIL>',
    ic_cart: api.ImgUrl + 'images/shop/ic_cart.png',
    page: 1,
    value: '',
    keywords: '',
    cartNumber: null,
    hasMore: true,
    sectionId: '',
    banner: '',
    hasContent: null,
    featuredList: [],
    otcConsultBuying: true,
    searchHeight: 0,
    scrollViewHeight: 0,
    showList: false,
    relevantList: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({ 
      sectionId: options.id,
      navTitle: options.title
    }, () => {
      this.getSearchHeight()
      this.getzoneGood()
    })
  },
  
  // 获取专区商品详情
  async getzoneGood() {
    try {
      if (this.loading) return
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
      })
      const params = { page: this.data.page, num: 10, keywords: this.data.keywords, orderBy: 'sort'}
      const response = await util.request(util.getRealUrl(api.zoneDetail, this.data.sectionId), params)
      const { code, data, msg } = response.data
      if (code === 0) {
        Toast.clear()
        this.loading = false
        this.setData({
          banner: data.banner,
          hasContent: data.hasContent,
          featuredList: this.data.page === 1 ? data.result : this.data.featuredList.concat(data.result),
          hasMore: this.data.page < data.totalPages,
          page: this.data.page + 1,
        })
      } else {
        Toast.fail(msg)
      }
    } catch (error) {
      throw new Error('请求失败：' + error)
    }
  },
  // 加入购物车
  handleaddCart() {
    this.shopCartNumber()
  },
  shopCartNumber() {
    util.request(api.cartsInfo).then(res => {
      const list = res.data.data.groups[0]?.items
      let number = null
      list && list.forEach(item => {
        number += item.quantity
      })
      this.setData({ cartNumber: number })
    })
  },
  onCart() {
    wx.navigateTo({
      url: '/pages/shoppingCart/index'
    })
  },
  
  // 输入内容变化时触发
  onChange(e) {
    if (!e.detail) {
      this.setData({
        relevantList: [],
        showList: false
      })
      return
    }
    this.setData({
      showList: true,
      value: e.detail
    })
    this.search(e.detail)
  },

  search(keywords) {
    util.request(util.getRealUrl(api.zoneDetail, this.data.sectionId), {
      keywords,
      page: 1
    }, 'get').then(res => {
      const list = res.data.data.result
      this.setData({
        relevantList: list,
        showList: list && list.length > 0 ? true : false
      })
    })
  },

  handleClick(event) {
    const { item } = event.currentTarget.dataset
    this.setData({
      page: 1,
      value: item.productName,
      keywords: item.productName,
      showList: false
    }, () => {
      this.getzoneGood()
    })
    
  },
  
  // 确定搜索时触发
  onSearch(event) {
    if (!this.value) return
    this.setData({ page: 1, keywords: event.detail }, () => {
      this.getzoneGood()
    })
  },

  onClear() {
    this.setData({ page: 1, keywords: '' }, () => {
      this.getzoneGood()
    })
  },

  // 跳转文章详情
  toArticle() {
    if (!this.data.hasContent) return
    wx.navigateTo({
      url: '/pages/article/articleDetail/index?type=zone&id=' + this.data.sectionId
    })
  },

  // 获取搜索区域高度
  getSearchHeight() {
    const query = wx.createSelectorQuery().in(this)
    query.select('#search').boundingClientRect(rect => {
      if (rect) {
        this.setData({
          searchHeight: rect.height,
          scrollViewTop: app.globalData.navBarHeight + rect.height,
          scrollViewHeight: app.globalData.screenHeight - (rect.height + app.globalData.navBarHeight)
        })
      }
    }).exec()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    const config = wx.getStorageSync('applicationConfig')
    this.setData({
      otcConsultBuying: config.config.otcConsultBuying
    })
    this.shopCartNumber()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({ 
      page: 1 
    }, () => {
      this.getzoneGood()
      setTimeout(() => {
        wx.stopPullDownRefresh()
      }, 1000)
    })
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (!this.data.hasMore) {
      return
    }
    this.getzoneGood()
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})