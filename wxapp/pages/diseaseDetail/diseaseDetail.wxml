<view class="container bg-color-gray-light">
	<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
	<view class="w100 bg-color-white p30">
		<view class="f32 b c333">就诊人信息 <text class="rd">*</text></view>
		<navigator open-type="navigate" hover-class="none" url="/pages/peopleContent/people/people?source=1">
			<view class="pt40">
				<view class="rel">
					<view class="c999 f32 lh44 " wx:if="{{info.inquirerId==''}}">选择就诊人</view>
					<block wx:else>
						<view class="c333 b f36">{{peopleInfo.name}} <text
								class="pl30 pr30 n c666 f32">{{peopleInfo.gender===1?'男':'女'}} <text
									class="pl20">{{peopleInfo.age}}</text></text><text
								class="tag n  f24 ">{{peopleInfo.relationName}}</text></view>
						<view class="f28 c333 pt10">
							{{!peopleInfo.phone && !peopleInfo.guardianPhone ? '-':(peopleInfo.phone?peopleInfo.phone:peopleInfo.guardianPhone) }}
						</view>
					</block>
					<image src="{{imgObject.ic_more_black}}" class="noIcon"></image>
				</view>
			</view>
		</navigator>
	</view>
  <!-- 一键带入 -->
  <view class="w100 bg-color-blue" wx:if="{{hasLastVisit}}">
    <text class="identify">识别到该就诊人上次问诊病情</text>
    <view class="one_btn" bind:tap="getConsultHistory">一键带入</view>
  </view>
	<view class="w100 bg-color-white p30">
		<view class="f32 b c333 mt20">是否就该病情到医院就诊过？<text class="rd">*</text></view>
		<view class="clearfix pt30">
			<radio-group class="radio" bindchange="radioChange" data-type="1">
				<view class="dib mr60 chooseItem">
					<radio value="1" checked="{{info.offlineSeeDoctor==1}}">
						<text class="f32 ml10 c333">是</text>
					</radio>
				</view>
				<view class="dib  chooseItem">
					<radio value="0" checked="{{info.offlineSeeDoctor==0}}">
						<text class="f32 ml10 c333">否</text>
					</radio>
				</view>
			</radio-group>
		</view>
		<view class="pt20 mt30 color-danger f28 lh40 bt1" wx:if="{{info.offlineSeeDoctor==0}}">
			线上只能对常见病、慢病进行复诊，如没有线下就诊过，请先前往线下医院就诊
		</view>
	</view>
	<view class="w100 bg-color-white p30 mt20 pb15" wx:if="{{info.offlineSeeDoctor==1}}">
		<view class="f32 b c333 pb30">线下就诊情况<text class="rd">*</text></view>
		<textarea placeholder="请描述您在线下医院的就诊情况、疾病诊断以及您的用药情况..." value="{{info.offlineDiagnosis}}" bind:input="textChange"
			placeholder-style="c999" wx:if='{{!showPopup}}'></textarea>
		<view class="drug pt30">
			<text wx:for="{{tag}}" wx:key="index" data-value="{{item}}" bindtap="tagChoose">{{item}}</text>
		</view>
	</view>
	<view class="w100 bg-color-white p30 mt20 pb15" wx:if="{{info.offlineSeeDoctor==1}}">
		<view class="f32 b c333 pb30">病情描述<text class="rd">*</text></view>
		<textarea placeholder="请描述您的主要症状(或体征)、部位、持续时间等..." value="{{info.description}}" data-key='description' bind:input="onInputVal"
			placeholder-style="c999" wx:if='{{!showPopup}}'></textarea>
	</view>

	<block wx:if='{{hainanConfig.config.collectSymptom}}'>
    <view class="w100 bg-color-white p30 mt20" wx:if="{{info.offlineSeeDoctor==1}}">
      <view class="f32 b c333 mt20">是否发热？<text class="rd">*</text></view>
      <view class="clearfix pt30">
        <radio-group class="radio" bindchange="handleChange" data-type="fever">
          <view class="dib mr60 chooseItem">
            <radio value="0" checked="{{info.symptom.fever==0}}">
              <text class="f32 ml10 c333">否</text>
            </radio>
          </view>
          <view class="dib mr60 chooseItem">
            <radio value="1" checked="{{info.symptom.fever==1}}">
              <text class="f32 ml10 c333">低热</text>
            </radio>
          </view>
          <view class="dib  chooseItem">
            <radio value="2" checked="{{info.symptom.fever==2}}">
              <text class="f32 ml10 c333">高热</text>
            </radio>
          </view>
        </radio-group>
      </view>
    </view>
    <view class="w100 bg-color-white p30 mt20" wx:if="{{info.offlineSeeDoctor==1}}">
      <view class="f32 b c333 mt20">是否为孕妇？<text class="rd">*</text></view>
      <view class="clearfix pt30">
        <radio-group class="radio" bindchange="handleChange" data-type="pregnant">
          <view class="dib mr60 chooseItem">
            <radio value="1" checked="{{info.symptom.pregnant==1}}">
              <text class="f32 ml10 c333">是</text>
            </radio>
          </view>
          <view class="dib mr60 chooseItem">
            <radio value="0" checked="{{info.symptom.pregnant==0}}">
              <text class="f32 ml10 c333">否</text>
            </radio>
          </view>
        </radio-group>
      </view>
    </view>
    <view class="w100 bg-color-white p30 mt20" wx:if="{{info.offlineSeeDoctor==1}}">
      <view class="f32 b c333 mt20">是否患有高血压？<text class="rd">*</text></view>
      <view class="clearfix pt30">
        <radio-group class="radio" bindchange="handleChange" data-type="hypertension">
          <view class="dib mr60 chooseItem">
            <radio value="1" checked="{{info.symptom.hypertension==1}}">
              <text class="f32 ml10 c333">是</text>
            </radio>
          </view>
          <view class="dib mr60 chooseItem">
            <radio value="0" checked="{{info.symptom.hypertension==0}}">
              <text class="f32 ml10 c333">否</text>
            </radio>
          </view>
        </radio-group>
      </view>
    </view>
    <view class="w100 bg-color-white p30 mt20" wx:if="{{info.offlineSeeDoctor==1}}">
      <view class="f32 b c333 mt20">是否患有糖尿病？<text class="rd">*</text></view>
      <view class="clearfix pt30">
        <radio-group class="radio" bindchange="handleChange" data-type="diabetes">
          <view class="dib mr60 chooseItem">
            <radio value="1" checked="{{info.symptom.diabetes==1}}">
              <text class="f32 ml10 c333">是</text>
            </radio>
          </view>
          <view class="dib mr60 chooseItem">
            <radio value="0" checked="{{info.symptom.diabetes==0}}">
              <text class="f32 ml10 c333">否</text>
            </radio>
          </view>
        </radio-group>
      </view>
    </view>
  </block>

	<view class="w100 bg-color-white p30 mt20">
    <view class="f32 b c333">图片资料({{info.offlineDiagnosisImgs.length}}/9)<text class="rd" hidden="{{info.dataLossTag!=0}}">*</text></view>
    <view class="f28 c999 pt10">上传检查报告、患处图片或实验室及医学影像检查等资料</view>
    <uploadImg imgList="{{info.offlineDiagnosisImgs}}" size="9" bind:imgArry="imgArry"></uploadImg>
    <!-- <view class="pt25">
      <van-checkbox value="{{ info.dataLossTag }}" bind:change="onChange" data-type="info.dataLossTag"
        icon-size="28rpx">
        <text class="c666 f28">资料不在身边或已丢失</text>
      </van-checkbox>
    </view> -->
  </view>
  <view class="flex p30 agreement">
    <!-- <view style="w100 flex">
      <van-checkbox value="{{ checked }}" bind:change="onChange" data-type="checked" icon-size="28rpx" custom-class="dib"><text class="c666 f24">我已阅读并同意</text><text class="f24 color-primary" data-type='6' catchtap='goAgreement'>《互联网问诊知情同意书》</text><text class="f24 color-primary" data-type='5' catchtap='goAgreement'>《{{company}}互联网医院服务协议》</text>
      </van-checkbox>
    </view> -->
    <view>
			<van-checkbox value="{{ checked }}" bind:change="onChecked" data-type="checked" icon-size="28rpx"
				custom-class="dib"><text class="c666 f24">我已阅读并同意</text><text class="f24 color-primary" data-type='6'
        bind:tap="handleShowAgm">《互联网问诊知情同意书》</text>
          <!-- <text class="f24 color-primary" data-type='5'
					catchtap='goAgreement'>《{{company}}互联网医院服务协议》</text> -->
			</van-checkbox>
		</view>

  </view>
</view>
<view class="fixed b0 l0 w100 bg-color-white pl30 pr30 confir">
	<button bindtap="pay"
		disabled="{{(info.offlineSeeDoctor===1 && info.offlineDiagnosis==='') || !info.description || info.offlineSeeDoctor===0 || !checked  || info.inquirerId=='' || (info.dataLossTag===0 && info.offlineDiagnosisImgs.length<=0) || (hainanConfig.config.collectSymptom && (info.symptom.fever == null || info.symptom.pregnant == null || info.symptom.hypertension == null || info.symptom.diabetes == null))}}">提交</button>
</view>

<agreement-popup id="ageePopup" type='6' bind:agreement='onAgreement' bind:closePopup='onclosePopup'></agreement-popup>