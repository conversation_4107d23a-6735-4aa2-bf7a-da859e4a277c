const api = require('../../config/api')
const util = require('../../utils/util')
Page({
  data: {
    ic_history_deleted: api.ImgUrl + 'images/<EMAIL>',
    keyword: '',
    list: [],
    noData: false
  },
  handleSearch(e) {
    this.search(e.detail)
  },
  search(keyword) {
    util.request(api.productList, {
      keyword,
      page: 1,
      offset: 1000
    }, 'get').then(res => {
      const list = res.data.data.result
      this.setData({
        list,
        noData: list.length === 0
      })
    })
  },
  onLoad: function(options) {
    this.search(options.text)
    this.setData({
      keyword: options.text
    })
  }
})
