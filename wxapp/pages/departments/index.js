// pages/addAddress/index.js
var api = require('../../config/api.js')
var util = require('../../utils/util')
Page({

  /**
	 * 页面的初始数据
	 */
  data: {
    result: {
      data: [],
      code: 0,
      msg: '没有数据'
    },
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '选择科室',
    isAddData: true,
    doctorId: 0,
    items: [],
    activeId: 0,
    activeIndex: 0
  },

  /**
	 * 生命周期函数--监听页面加载
	 */
  onLoad: function(options) {
    this.getDdepartments()
  },
  getDdepartments() {
    util.showLoading({
      title: '加载中',
      mask: true
    }
    )
    util.request(api.citydepart, {}, 'GET')
      .then(res => {
        if (res.data.code === 0) {
          this.data.items = res.data.data.departments.list.map(item => {
            item.children = (item.childs && item.childs.length > 0) ? item.childs : [item]
            item.children = item.children.map(child => {
              return {
                text: child.name,
                id: child.id
              }
            })
            return {
              text: item.name,
              children: item.children
            }
          })
        }
        this.setData({
          items: this.data.items
        })
        util.hideLoading()
      })
  },
  navClick(e) {
    this.setData({
      activeIndex: e.detail.index
    })
  },
  itemClick(data) {
    console.log(data, 66)
    const { id, text } = data.detail
    wx.navigateTo({
      url: `/pages/doctorList/index?departmentId=${id}&text=${text}`
    })
  },
  /**
	 * 生命周期函数--监听页面初次渲染完成
	 */
  onReady: function() {

  },

  /**
	 * 生命周期函数--监听页面显示
	 */
  onShow: function() {

  },

  /**
	 * 生命周期函数--监听页面隐藏
	 */
  onHide: function() {

  },

  /**
	 * 生命周期函数--监听页面卸载
	 */
  onUnload: function() {

  },

  /**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
  onPullDownRefresh: function() {

  },

  /**
	 * 页面上拉触底事件的处理函数
	 */
  onReachBottom: function() {

  },

  /**
	 * 用户点击右上角分享
	 */
  onShareAppMessage: function() {

  }
})
