/* pages/aboutAs/index.wxss */
.about {
    width: 100%;
    height: 100%;
    background-color: #f8f8f8!important;
}
.version {
  width: 100%;
  height: 360rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.logo_bg {
  width: 132rpx;
  height: 132rpx;
  background: #ffffff;
  box-shadow: 0rpx 0rpx 8rpx 0rpx rgba(218, 218, 218, 0.5);
  border-radius: 20rpx;
  margin-bottom: 28rpx;
}
.logo_img {
  width: 100%;
  height: 100%;
}
.v_text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
  text-align: center;
}
.item {
    background-color: #ffffff;
}
.item image {
  display: block;
  float: right;
  width: 44rpx;
  height: 44rpx;
}
.c333 {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 32rpx;
  color: #333333;
  line-height: 44rpx;
  text-align: left;
  font-style: normal;
}
.line {
    margin: 0 28rpx;
    height: 1rpx;
    background: #EEEEEE;
}
