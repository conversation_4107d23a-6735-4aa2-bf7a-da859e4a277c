page{
    background-color: var(--bgColor);
}
.tab{
    width: 100%;
    height: 84rpx;
    background: #fff;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
}
.tab view{
    text-align: center;
    font-size: 28rpx;
    color: #666666;
    height: 84rpx;
    line-height: 84rpx;
    /* width: 25%; */
}
.tab view text{
    display: inline-block;
    height: 100%;
    position: relative;
}
.tab view text.cur{
    color: var(--themeColor);
    font-weight: bold;
}
.tab view text.cur::after{
    position: absolute;
    content: '';
    width: 40rpx;
    height: 8rpx;
    border-radius: 2rpx;
    background: var(--themeColor);
    left: 50%;
    margin-left: -20rpx;
    bottom: 0rpx;
}

.list{
    box-sizing: border-box;
    padding: 112rpx 28rpx 28rpx;
}
.item{
    width: 694rpx;
    background: #FFFFFF;
    border-radius: 20rpx;
    padding: 20rpx;
    margin-bottom: 28rpx;
}
.title{
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
    line-height: 44rpx;
    padding-bottom: 12rpx;
}
.desc{
    padding-top: 8rpx;
    font-size: 28rpx;
    font-weight: 400;
    color: #666666;
    line-height: 40rpx;
}
