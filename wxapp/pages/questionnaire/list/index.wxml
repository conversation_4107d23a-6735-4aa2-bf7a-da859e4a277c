<navbar  isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
<view class="pb20">
    <view class="tab flex" style="top:{{ 44 + statusBarHeight}}px;">
        <view wx:for="{{orderStatus}}" class="flex1 flex_c_m" wx:key="index">
            <text class=" {{listQuery.type == item.id?'cur':''}}" bindtap="tabClick" data-id="{{item.id}}">{{item.name}}</text>
        </view>
    </view>
    <view class="list" wx:if="{{list.length>0}}">
        <view class="item" wx:for="{{list}}" wx:key="index" bindtap="handleDetail" data-item="{{item}}">
            <view class="title">{{item.formName}}</view>
            <view class="desc">咨询师：{{item.counselorName}}</view>
            <view class="desc">发送时间：{{item.createdAt}}</view>
            <view class="desc" wx:if="{{listQuery.type==1}}">提交时间：{{item.submitAt}}</view>
        </view>
    </view>
    <view wx:if="{{!list.length}}" class="flex_line_c no_msg_box">
        <image class="no_msg" src="{{nomes}}"></image>
        <view class="f28 c666">暂无数据</view>
    </view>
</view>
