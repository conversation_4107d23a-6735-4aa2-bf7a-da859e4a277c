// pages/from/fromDetail/index.js
const api = require('../../../config/api.js')
var util = require('../../../utils/util')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    followUpFormId: null,
    url: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    const { _o } = util.headerParams
    const { counselorFollowId, followUpFormId, patientId, preConsultId, counselorId } = options
    const url = api.WebViewUrl + `/#/followDetail?counselorId=${counselorId}&followUpFormId=${followUpFormId}&counselorFollowId=${counselorFollowId}&origin=${_o}&patientId=${patientId}&preConsultId=${preConsultId}&token=` + wx.getStorageSync('token')
    this.setData({
      url, counselorFollowId, followUpFormId, patientId, preConsultId
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {

  }
})
