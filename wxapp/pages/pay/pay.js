// pages/pay/pay.js
const api = require('../../config/api')
const util = require('../../utils/util')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    doctor: {},
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '支付'
  },
  getDoctorDetail() {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    util.request(api.doctorDetail, { doctorId: this.data.doctorId }, 'post', 2)
      .then(res => {
        wx.hideToast()
        if (res.data.code == 0) {
          this.setData({
            doctor: res.data.data
          })
        } else {
          util.showToast({
            icon: 'none',
            title: res.data.msg
          })
        }
      })
      .catch(res => {
      })
  },
  // 缓存就诊数据
  setInquirerData() {
    const inquirerData = {
      offlineDiagnosisImgs: app.globalData.info.offlineDiagnosisImgs,
      offlineDiagnosis: app.globalData.info.offlineDiagnosis,
      conditionDesc: app.globalData.info.description
    }
    const key = 'inquirer_' + this.data.inquirerId
    try {
      wx.setStorageSync(key, inquirerData) 
    } catch (error) {
      console.log(error)
    }
  },
  payinfo() {
    var that = this
    var data = {
      doctorId: that.data.doctorId,
      price: that.data.type == 1 ? that.data.doctor.consultCost : that.data.doctor.videoCost,
      recordId: that.data.recordId,
      inquirerId: that.data.inquirerId
      // offlineDiagnosisImgs: app.globalData.info.offlineDiagnosisImgs,
      // offlineDiagnosis: app.globalData.info.offlineDiagnosis,
      // conditionDesc: app.globalData.info.description
    }
    if (that.data.type != 1) {
      delete data.recordId
      data.offlineDiagnosisImgs = app.globalData.info.offlineDiagnosisImgs
      data.offlineDiagnosis = app.globalData.info.offlineDiagnosis
      data.conditionDesc = app.globalData.illnessDescription || app.globalData.info.description
    }
    var ApiUrl = that.data.type == 1 ? api.payinfo : api.videoPayInfo
    util.showLoading({
      title: 'loading',
      mask: true
    })
    util.request(ApiUrl, data, 'post', 1)
      .then(res => {
        if (res.data.code == 0) {
          var wxPayData = res.data.data
          util.hideLoading()
          wx.requestPayment({
            'timeStamp': wxPayData.timeStamp,
            'nonceStr': wxPayData.nonceStr,
            'package': wxPayData.package,
            'signType': 'MD5',
            'paySign': wxPayData.paySign,
            'success': function(res) {
              util.showToast({
                title: '支付成功',
                icon: 'success',
                duration: 1000,
                success: function() {
                  setTimeout(() => {
                    if (that.data.type == 1) {
                      wx.navigateTo({
                        url: '/pages/consult/chat/chat?doctorId=' + that.data.doctorId + '&pageNum=4'
                      })
                      // 缓存就诊人病情数据
                      this.setInquirerData()
                    } else {
                      app.globalData.consultType = 2
                      app.globalData.doctorName = that.data.doctor.name
                      wx.switchTab({
                        url: '/pages/consult/index/index'
                      })
                      // 缓存就诊人病情数据
                      this.setInquirerData()
                    }
                  }, 1000)
                }
              })
            },
            'fail': function(res) {
              util.showToast({
                title: '支付失败',
                icon: 'none'
              })
            },
            'complete': function(res) {}
          })
        } else {
          util.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
      .catch(res => {})
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.setData({
      doctorId: options.doctorId,
      type: options.type,
      recordId: options.recordId,
      inquirerId: options.inquirerId
    })
    console.log(app.globalData.info)
    this.getDoctorDetail()
  },
  goAgreement() {
    wx.navigateTo({
      url: '/pages/agreement/index?type=5'
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  }

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage: function () {

  // }
})
