<!--pages/confirmOrder/confirmOrder.wxml-->
<view class="bg-color-gray-light" style="padding-bottom: 140rpx;">
	<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
	<view class="tips">温馨提示：“根据《药品经营质量管理规范》第三章 第一百七十七条规定，除药品质量原因外，药品一经售出，不得退换”。</view>
	<view class="bg-color-white">
		<navigator open-type="navigate" class="pt40 pb40 pl30 pr30 rel flex_m" hover-class="none"
			url="/pages/address/index?type=2">
			<image style="width:44rpx;height:44rpx" src="{{imgObject.ic_address}}"></image>
			<view wx:if="{{info.shippingInfo!=null}}" class="flex_m">
				<view class="flex1 ml20">
					<view class="f32 c333">{{info.shippingInfo.receiver}} <text class="ml30">{{info.shippingInfo.phone}}</text>
					</view>
					<view class="f28 c333 mt10">{{info.shippingInfo.fullAddress}}</view>
				</view>
			</view>
			<view class="flex_line_c_m flex1" wx:else>
				<view class="f32 c333 b">请选择收货地址</view>
			</view>
			<image style="width:44rpx;height:44rpx;right:30rpx" class="abs" src="{{imgObject.ic_more_black}}"></image>
		</navigator>
	</view>
	<image class='w100' style="height:4rpx; display: block;" src="{{imgObject.img_order_bar}}"></image>
	<view class="p30 mt20 bg-color-white">
		<view class="f28 c333 b mt20">
			Rp：共{{orderDetail.products.length}}味药
		</view>
		<view class="drug_encrypt flex_c_m f24 c666">
			共{{orderDetail.products.length}}味药，支付后可查看处方
		</view>
		<view class="f28 c666 mt30">
			【辅料】<text wx:for='{{orderDetail.accessoriesList}}'>{{index>0?',':''}}{{item.name}}  {{item.dose}}</text>
		</view>

	</view>
	<view class="bg-color-white p30 mt20">
		<view class="f28 flex_lr_m">
			<view class="c999">
				药费（共{{orderDetail.doseNum}}剂）
			</view>
			<view class="c333">
				{{drugFunc}}
			</view>
		</view>
		<view class="f28 flex_lr_m mt10" wx:if="{{orderDetail.dosageForm !== 1 && processingFlag == 1}}">
			<view class="c999">
				代煎费
			</view>
			<view class="c333">
			{{diagnosisFunc}}
			</view>
		</view>
		<view class="f28 flex_lr_m mt10" wx:if="{{orderDetail.dosageForm == 1}}">
			<view class="c999">
				制作费
			</view>
			<view class="c333">
			{{orderDetail.price.productionFee/100}}元
			</view>
		</view>
		<view class="f28 flex_lr_m mt10">
            <view class="c999">
                运费
            </view>
            <view wx:if="{{orderDetail.price.freight/100 > 0}}" class="c333">
            {{orderDetail.price.freight/100}}元
            </view>
            <view wx:else class="c333">包邮</view>
        </view>
		<view class="tr pt30 f28">
			总计：<text class="color-danger b">¥{{totalCount}}</text>
		</view>
	</view>
	<view class="mt20 bg-color-white p30">
		<view class="pb30 f28 c999" wx:if="{{orderDetail.dosageForm !== 1 && orderDetail.processingFlag == 1}}">
			是否代煎
			<view class="fr c333 f28">
				<van-radio-group value="{{ processingFlag }}" bind:change="onRadioChange">
					<view class="flex_m">
						<van-radio name="{{1}}" icon-size='15px' >是</van-radio>
						<van-radio name="{{0}}" icon-size='15px' >否</van-radio>
					</view>
				</van-radio-group>
			</view>
		</view>
		<view class="pb30 f28 c999">发货门店 <text class="fr c333 f28">{{orderDetail.warehouseName}}</text></view>
		<view class="pb30 f28 c999">配送方式 <text class="fr c333 f28">{{orderDetail.shippingMethod[transFlag].shippingMethodName}}</text></view>
		<view class="f28 c999">支付方式 <text class="fr c333 f28">微信支付</text></view>
	</view>
	<view class="mt20 bg-color-white p30">
		<view class="mb20 f28 b">订单备注</view>
		<textarea class="remark p20 box_bb f28 container-radius bg-color-gray-light" placeholder="请输入备注信息…"
			placeholder-class="c999" bindinput="changeNote"></textarea>
	</view>
	<!-- <view class="p30 f28 c666 lh40">国家法规要求：“除药品质量原因外,药品一经售出,不得退换”。</view> -->
	<view class="fixed b0 l0 w100 bg-color-white pl30 pr30 confir zx999 flex_lr_m">
		<view class="flex_m c333 f24">实付款：<text class="f36 b color-danger">¥{{totalCount}}</text>
		</view>
		<view class="fixedbutton f26 container-radius" bindtap="doPreOrder" disabled="{{info.shippingInfo===null}}">提交订单
		</view>
	</view>
</view>
<view wx:if='{{overlay}}' class="wrapper-overlay">
	{{errMessage}}
</view>
