var api = require('../../config/api.js')
var util = require('../../utils/util')
const app = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '病历档案',
    list: [],
    inquirerId: '',
    inquirerList: [],
    isEmpty: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.getList()
  },

  // 就诊人列表
  async getList() {
    const data = await util.getPeopleList()
    data.forEach((element) => {
      element.text = element.name
      element.value = element.inquirerId
    })
    this.setData(
      {
        inquirerList: data,
        inquirerId: data[0].inquirerId
      },
      () => {
        this.getCaseList()
      }
    )
  },
  // 获取病历列表
  async getCaseList() {
    wx.showLoading({ title: '加载中...' })
    const params = { inquirerId: this.data.inquirerId, pageSize: 10 }
    console.log('params', params)
    const { data } = await util.request(api.caseList, params, 'post', 2, false)
    console.log('data', data)
    if (data.code === 0) {
      const { result } = data.data
      result.forEach((item) => {
        item.diagnosis = item.doctorMedicalRecord.diagnosisList.join('')
        item.relationName = this.data.inquirerList.find((v) => v.inquirerId === this.data.inquirerId).relationName
      })
      this.setData({
        list: data.data.result,
        isEmpty: result.length > 0 ? false : true
      })
      wx.hideLoading()
    } else {
      wx.hideLoading()
      util.showErrorToast(data.msg)
    }
  },
  // 监听选择器变化
  handleChange({ detail }) {
    this.setData(
      {
        inquirerId: detail
      },
      () => {
        this.getCaseList()
      }
    )
  },

  toDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: '/pages/caseDetail/index?id=' + id
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {}
})
