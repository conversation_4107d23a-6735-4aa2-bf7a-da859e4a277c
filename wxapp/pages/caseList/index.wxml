<view class="container bg-color-gray-light">
    <navbar isBack="{{true}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
    <view class="select-warp">
        <view class="select-people">就诊人</view>
        <view class="select-line"></view>
        <van-dropdown-menu overlay="{{false}}" close-on-click-outside active-color="#1989fa">
            <van-dropdown-item title-class="my-dropdown-title" popup-style="width:280rpx;margin-left:180rpx;margin-top:10rpx;border-radius:none" value="{{ inquirerId }}" options="{{ inquirerList }}" bind:change="handleChange" />
        </van-dropdown-menu>
    </view>
    <view class="item bg-color-white m20" wx:for="{{ list }}" wx:key="*this" data-id="{{item.id}}" bindtap="toDetail">
        <view class="label">{{ item.relationName }}</view>
        <view class="c333 f32 mb8">就诊人：{{ item.name }}</view>
        <view class="c333 f32 mb8">诊断：{{ item.diagnosis }}</view>
        <view class="c333 f32">时间：{{ item.finishTimeDate }}</view>
    </view>
    <view wx:if="{{isEmpty}}" class="noData">
        <image class="imgEmpty" src="../../static/images/img_blank_nomessage.png" mode="aspectFill" />
        <view class="f28 c666 mt40">暂无数据</view>
    </view>
</view>