// pages/addPeople/addPeople.js
const api = require('../../../config/api')
const util = require('../../../utils/util')
const check = require('../../../utils/check')
const app = getApp()
Page({
  ...check,
  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    navTitle: '预约详情',
    type: 1,
    id: null,
    dataItem: {},
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    console.log('options', options)
    this.setData({
      id: +options.id,
    })
    await this.hpvActivityOrder()
  },
  onShow: function() {
    console.log('show1')
  },
  // 获取活动预约详情
  hpvActivityOrder() {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    util.request(api.hpvActivity + `/order/detail`, {
      id: this.data.id
    }, 'GET', 2)
    .then(res => {
      if (res.data.code === 0) {
        let obj =  this.maskNumber(res.data.data)
        this.setData({
          dataItem: obj,
        })
        util.hideToast()
      } else {
        util.showToast({
          title: res.data.msg
        })
      }
    })
    util.hideLoading()
  },
  maskNumber(data) {
    // 检查输入是否为有效的手机号码格式
    if (/^\d{11}$/.test(data.checkerPhone)) {
      // 脱敏处理
      data.checkerPhone = data.checkerPhone.slice(0, 3) + "*****" + data.checkerPhone.slice(-4)
    }
    if(data.checkerIdCard && data.checkerIdCard.length === 18) {
      const reg = /^(.{6})(?:\d+)(.{4})$/;
      data.checkerIdCard = data.checkerIdCard.replace(reg, '$1********$2')
    }
    return data
  },
  call(e) {
    wx.makePhoneCall({
      phoneNumber: e.currentTarget.dataset.phone
    })
  },
})