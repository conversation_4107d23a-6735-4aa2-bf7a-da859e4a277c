<!--pages/addPeople/addPeople.wxml-->
<view class="container">
	<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>

	<view class="flex_m wrap container-top">
		<image src="{{dataItem.activityImgUrl}}" mode="aspectFill" class="img"/>
		<text class="text-name"> {{dataItem.activityTitle}}</text>
	</view>
	<view class="w100 bg-color-white p30 mt20">
		<view class="f32 b c333">门店信息</view>
		<view class="pt20">
			<view class="bg-color-white">
				<view class="lh44 f32 shop-title mt8">{{dataItem.storeName}}</view>
				<view class="flex_lr_m mt8">
					<view>
						<view class="flex_wrap mt8">
							<image src="../../../static/images/home/<USER>" class="shop-icon"></image>
							<text lines="1" class="c666 f24 lh36">营业时间：{{dataItem.businessTime}}</text>
						</view>
						<view class="flex_wrap">
							<image src="../../../static/images/home/<USER>" class="shop-icon shop-loction-icon"></image>
							<text lines="1" class="c666 f24 lh36 shop-address">{{dataItem.address}}</text>
						</view>
					</view>
					<view class="shop-phone-box flex_c_m" wx:if="{{dataItem.contactsPhone}}" data-phone="{{dataItem.contactsPhone}}" bindtap="call">
						<image src="../../../static/images/home/<USER>" class="shop-phone-icon"></image>
					</view>
				</view>
			</view>
		</view>
	</view>
	<view class="w100 bg-color-white pt30 pl30 pr30 mt20">
		<text class="f32 b c333">预约信息</text>
		<view class="item w100 bb1 flex_lr_m">
			<view class="c666 pt30 pb30">姓名</view>
			<view class="f32 c333 pt30 pb30">{{dataItem.checkerName}}</view>
		</view>
		<view class="item w100 bb1 flex_lr_m">
			<view class="c666 pt30 pb30">身份证号</view>
			<view class="f32 c333 pt30 pb30">{{dataItem.checkerIdCard}}</view>
		</view>
		<view class="item w100 bb1 flex_lr_m">
			<view class="c666 pt30 pb30">手机号码</view>
			<view class="f32 c333 pt30 pb30">{{dataItem.checkerPhone}}</view>
		</view>
		<view class="item w100 bb1 flex_lr_m">
			<view class="c666 pt30 pb30 w230">预约日期</view>
			<view class="f32 c333 pt30 pb30">{{dataItem.checkTime}}</view>
		</view>
		<view class="item w100 flex_lr_m">
			<view class="c666 pt30 pb30 w230">提交时间</view>
			<view class="f32 c333 pt30 pb30">{{dataItem.createdAt}}</view>
		</view>
	</view>
</view>