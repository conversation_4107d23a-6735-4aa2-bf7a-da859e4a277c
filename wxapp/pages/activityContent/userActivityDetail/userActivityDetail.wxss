/* pages/addActivity/addActivity.wxss */
page{
  background: #F8F8F8;
}
.wrap{
  background: #FFF;
  padding: 28rpx;
  margin-bottom: 20rpx;
}
.shop-title {
  width: 100%;
  color: #333;
  font: 600 32rpx/44rpx 'PingFangSC, PingFang SC';
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.shop-address{
  width: 502rpx;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; 
}

.container-top{

}
.container-top image{
  width: 148rpx;
  height: 148rpx;
}
.container-top .text-name{
  color: #2B2827;
  font: 500 32rpx/44rpx 'PingFangSC, PingFang SC';
  padding-left: 28rpx;
  padding-right: 28rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; 
  overflow: hidden;
  width: 502rpx;
}
.check-time{
  position: absolute;
  right: 20px;
}
.noIcon{
  width: 44rpx;
  height: 44rpx;
}
input {
  font-size: 32rpx;
}
.textright{
  text-align: right;
}
.mt8{
  margin-top: 8rpx;
}
.mb8{
  margin-bottom: 8rpx;
}
.shop-icon{
  width: 24rpx;
  height: 24rpx;
  margin-right: 12rpx;
}
.hop-loction-icon{
  width: 22rpx;
  height: 26rpx;
}
.phone-icon {
  width: 56rpx;
  height: 56rpx;
  margin-right: 28rpx;
}
.flex_wrap{
  align-items: center;
}
.shop-phone-box{
  width: 56rpx;
  height: 56rpx;
  background: #F8F8F8;
  border-radius: 18rpx;
}
.shop-phone-icon{
  width: 28rpx;
  height: 28rpx;
}
.moreRight{
  width: 20rpx;
  height: 44rpx;
}
