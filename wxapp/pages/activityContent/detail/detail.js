const util = require('../../../utils/util')
const api = require('../../../config/api')
const app = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    id: null,
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '活动详情',
    current: 1,
    dataItem: {},
  },
   // 切换产品图
  handleSwiperChange (e) {
    const n = e.detail.current
    this.setData({
      current: n + 1
    })
  },
  goback() {
    const { routerPage } = this.data
    setTimeout(() => {
      if (routerPage > 1) {
        wx.navigateBack({
          delta: 1
        })
      } else {
        wx.switchTab({
          url: '/pages/home/<USER>'
        })
      }
    }, 1000)
  },
  addActivity() {
    wx.navigateTo({
      url: `/pages/activityContent/addActivity/addActivity?type=1&source=2&id=${this.data.id}`
    })
  },
  hpvActivity() {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    util.request(api.hpvActivity + `/${this.data.id}`, {
      cityId: app.globalData.cityId
    }, 'GET', 2)
    .then(res => {
      if (res.data.code === 0) {
        // 富文本图片自适应
        res.data.data.content =  res.data.data.content.replace(/\<img/gi, '<img style="max-width:100%;height:auto;margin: 10px auto;" ')
        this.setData({
          dataItem: res.data.data,
        })
        util.hideToast()
      } else {
        util.showToast({
          title: res.data.msg
        })
      }
    })
    util.hideLoading()
  },
  onLoad: function(options) {
    this.setData({
      id: options.id * 1,
    })
  },
  onShow: function() {
    this.hpvActivity()
  },
})