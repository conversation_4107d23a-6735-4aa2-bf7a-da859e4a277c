<view class='container'>
  <navbar isBack='{{isBack}}' home='{{!isBack}}' backgroundColor='{{backgroundColor}}' navTitle='{{navTitle}}'></navbar>
  <view class="wrap container-top">
    <view class="banner-wrap">
      <swiper autoplay="true" bindchange="handleSwiperChange">
        <swiper-item wx:for="{{dataItem.imgUrlList}}" wx:key="index">
          <image src="{{item}}" mode="aspectFill" class="img"/>
        </swiper-item>
      </swiper>
      <view class="swiper-current" wx:if="{{ dataItem.imgUrlList.length > 1 }}">
        {{current}}/{{dataItem.imgUrlList.length}}
      </view>
    </view>

    <text class="name">{{ dataItem.title }}</text>
  </view>
  <view class="wrap container-middle p10">
    <view class="title-wrap flex">
      <image src="/static/images/ic_img.png" mode="aspectFill" class="img"/>
      <text class="title">图文详情</text>
    </view>
		<view class="pb30 pt20">
			<rich-text nodes="{{dataItem.content}}"></rich-text>
		</view>
  </view>

</view>

<view class="fixed b0 l0 w100 bg-color-white pl30 pr30 confir">
	<button bindtap="addActivity" >活动预约</button>
</view>