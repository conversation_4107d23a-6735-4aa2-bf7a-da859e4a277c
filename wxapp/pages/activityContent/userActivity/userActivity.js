// pages/people/people.js
var api = require('../../../config/api.js')
const util = require('../../../utils/util.js')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list: [],
    imgObject: {
      ic_address_edit: api.ImgUrl + 'images/ic_address_edit.png',
      ic_address_deleted: api.ImgUrl + 'images/ic_address_deleted.png',
      nomes: api.ImgUrl + 'images/nomes.png'
    },
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '活动预约',
    listQuery: {
      page: 1
    },
    loadComplete: null
  },
  // 活动详情            
  activityDetail: function(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/activityContent/userActivityDetail/userActivityDetail?id=${id}`
    })
  },
 
  // 活动预约列表
  async getList(type) {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    if (type !== 1 && type !== 2) {
      type = 1
    }
    var that = this
    try {
      const { data } = await util.request(
        api.hpvActivity + `/order/list`,
        that.data.listQuery,
        'get',
        '1'
      )
      if (!data) {
        return false
      }
      const result = data.data
      console.log(result, '======data======')
      if (data.code === 0) {
        that.setData({
          list:
            type === 1 ? result.result : that.data.list.concat(result.result),
          loadComplete: result.hasNext
        })
      } else {
        util.showToast({
          icon: 'none',
          title: data.msg
        })
      }
      return true
    } catch (error) {
      return false
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.setData({
      type: options.type * 1
    })
    this.getList(1)
  },
  onReachBottom: function() {
    if (this.data.loadComplete) {
      this.setData({
        ['listQuery.page']: ++this.data.listQuery.page
      })
      this.getList(2)
    }

  },
})
