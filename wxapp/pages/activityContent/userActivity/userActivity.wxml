<!--pages/people/people.wxml-->
<view class="list" style="padding-bottom:160rpx">
	<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
	<view wx:if="{{list.length>0}}">
		<view class="item bg-color-white m20" wx:for="{{list}}" wx:key="index"
			data-id="{{item.id}}" data-index="{{index}}" catchtap="activityDetail">
			<view data-index="{{index}}">
				<view class="name c333">{{ item.activityTitle }}</view>
				<view class="text c666">预约门店：{{ item.storeName }}</view>
				<view class="text c666">预约日期：{{ item.checkTime }}</view>
				<view class="text c666">预约人：{{ item.checkerName }}</view>
			</view>
			
		</view>
	</view>
</view>
