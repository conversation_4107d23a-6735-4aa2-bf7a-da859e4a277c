// pages/addPeople/addPeople.js
const api = require('../../../config/api')
const util = require('../../../utils/util')
const check = require('../../../utils/check')
const app = getApp()
Page({
  ...check,
  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    navTitle: '活动预约',
    type: 1,
    id: null,
    shop: {

    },
    msgData: {
      name: '',
    },
    dataItem: {},
    checkerIdCard: null,
    checkerName: null,
    checkerPhone: null,
    checkTime: '',
    imgObject: {
      ic_my_more: api.ImgUrl + 'images/ic_my_more.png',
      ic_more_grey: api.ImgUrl + 'images/ic_more_grey.png',
    },
    shopitem: {
      id: ''
    },
    orderTimeList: [],
    selectedDates: [],
    formatter(day) {
      return day
    },
    isCanOrder: true
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    console.log('options', options)
    this.setData({
      id: +options.id,
    })
    await this.hpvActivity()
    await this.formatterFuc()
  },
  onShow: async function() {
    if(this.data.checkTime){
      this.setData({
        checkTime: ''
      })
    }
  },
  // 获取活动详情
  hpvActivity() {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    util.request(api.hpvActivity + `/${this.data.id}`, {
      cityId: app.globalData.cityId
    }, 'GET', 2)
    .then(res => {
      if (res.data.code === 0) {
        this.setData({
          dataItem: res.data.data,
          shopitem: res.data.data.storeVO,
          orderTimeList: res.data.data.storeVO?.orderTimeList || []
        })
        util.hideToast()
      } else {
        util.showToast({
          title: res.data.msg
        })
      }
    })
    util.hideLoading()
   },
   // 提交活动
  submitOrder() {
    const that = this
    if(!this.data.dataItem.canOrder){
      const notOrderTips = this.data.dataItem.notOrderTips
      wx.showModal({
        title: '温馨提示',
        confirmText: '我知道了',
        showCancel: false,
        content: `${notOrderTips}`,
        success(res) {
          that.setData({
            isCanOrder: false
          })
        }
      })
      return
    }
    if (!this.data.checkerName) {
      util.showToast({
        title: '姓名不为空',
        icon: 'none'
      })
      return
    }
    if (this.data.checkerName.length > 6) {
      util.showToast({
        title: '请填写正确的姓名',
        icon: 'none'
      })
      return
    }
    var reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
    if (!reg.test(this.data.checkerPhone) || !this.data.checkerPhone) {
      util.showToast({
        title: '请输入正确的手机号码',
        icon: 'none',
      })
      return
    }
    if(!this.isValidIdCard(this.data.checkerIdCard)){
      util.showToast({
        title: '身份证号码格式不正确',
        icon: 'none'
      })
      return
    }
    if (!this.data.checkerIdCard) {
      util.showToast({
        title: '请输入身份证号',
        icon: 'none'
      })
      return
    }
    const params = {
      storeId: this.data.shopitem.id,
      id: this.data.id,
      checkerPhone: this.data.checkerPhone,
      checkerName: this.data.checkerName,
      checkTime: this.data.checkTime,
      checkerIdCard: this.data.checkerIdCard
    }
    const sysInfo = wx.getSystemInfoSync()
    const tokenKey = wx.getStorageSync('tokenKey')
    const header = {
      'content-type': 'multipart/form-data; boundary=XXX'
    }
    const headerParams = {
      // '_p': 0,
      '_m': sysInfo.model,
      '_o': 0,
      '_w': 1
    }
    if (wx.getStorageSync('token')) {
      header[tokenKey] = wx.getStorageSync('token')
    }
    wx.request({
      url: api.hpvActivity + `/${this.data.id}/submitOrder`,
      method: 'POST',
      header: Object.assign(header, headerParams),
      data: '\r\n--XXX' +
            '\r\nContent-Disposition: form-data; name="storeId"' +
            '\r\n' +
            '\r\n' + params.storeId +
            '\r\n--XXX' +
            '\r\nContent-Disposition: form-data; name="id"' +
            '\r\n' +
            '\r\n' + params.id +
            '\r\n--XXX' +
            '\r\nContent-Disposition: form-data; name="checkerPhone"' +
            '\r\n' +
            '\r\n' + params.checkerPhone +
            '\r\n--XXX' +
            '\r\nContent-Disposition: form-data; name="checkerName"' +
            '\r\n' +
            '\r\n' + params.checkerName +
            '\r\n--XXX' +
            '\r\nContent-Disposition: form-data; name="checkTime"' +
            '\r\n' +
            '\r\n' + params.checkTime +
            '\r\n--XXX' +
            '\r\nContent-Disposition: form-data; name="checkerIdCard"' +
            '\r\n' +
            '\r\n' + params.checkerIdCard +
            '\r\n--XXX--'
      , 
      success: function (res) {
        console.log('res', res)
        if(res.data.code === 100){
          wx.showModal({
            title: '',
            confirmText: '我知道了',
            showCancel: false,
            content: `${res.data.msg}`,
            success(res) {
              that.setData({
                isCanOrder: false
              })
            }
          })
          return
        }
        util.showToast({
          icon: 'none',
          title: '提交成功'
        })
        wx.requestSubscribeMessage({
          tmplIds: ['Q8eMomrUruU2GpXpVv-imvDJLsXvBKw2eZPxwH7louE'],
          success: (res) => {
            console.log('用户同意订阅消息：', res);
          },
          fail(err) {
            console.log('用户拒绝订阅消息：', err); 
          }
        })
        that.gotoActivity()
      }
    })
  },
  gotoActivity(){
    wx.reLaunch({
      url: `/pages/activityContent/userActivity/userActivity`
    })
  },
  getFormData(object) {
    const formData = new FormData();
    Object.keys(object).forEach(key => {
      const value = object[key];
      if(Array.isArray(value)) {
        value.forEach((subValue, i) => {
          formData.append(key + `[${i}]`, subValue)
        })
      } else {
        formData.append(key, object[key])
      }
    })
    return formData;
  },
  // 身份证号同步
  idCardChange(e) {
    if (e.detail.value.length == 18 && !this.isValidIdCard(e.detail.value)) {
      util.showToast({ 'title': '身份证号码格式不正确' })
      return
    }
    this.setData({
      checkerIdCard: e.detail.value
    })
  },
  // 文本框内容同步
  inputChange(e) {
    var type = e.currentTarget.dataset.type
    this.setData({
      [type]: util.filterEmoji(e.detail.value)
    })
  },
  // 预约日期
  onDisplay() {
    this.formatterFuc()
    this.setData({ show: true });
  },
  onClose() {
    this.setData({ show: false });
  },
  formatterFuc() {
    const that = this
    const checkTime = this.data.checkTime
    this.setData({
      formatter: function(day) {
        const date = day.date;
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const dayNum = date.getDate();
        const dayStr = `${year}-${month < 10 ? '0' + month : month}-${dayNum < 10 ? '0' + dayNum : dayNum}`;
        const isAvailable = that.data.orderTimeList.includes(dayStr);
        day.bottomInfo = isAvailable ? '可约' : '不可约'
        if (day.type === 'selected' && checkTime) {
          return day;
        }
        day.type = isAvailable ? '' : 'disabled'
        return day;
      }
    })
  },
  formatDate(date) {
    date = new Date(date);
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份是从0开始的，所以要加1
    const day = date.getDate().toString().padStart(2, '0');
    return `${date.getFullYear()}-${month}-${day}`;
  },
  onConfirm(event) {
    this.setData({
      show: false,
      checkTime: this.formatDate(event.detail),
    });
  },
  // 设置默认
  onPhoneblur() {
    wx.hideKeyboard()
  },
  async getShopList() { //type=1 直接请求 =2分页请求
    var that = this
    var parmas = {
      page: 1,
      cityId: app.globalData.cityId,
    }
    const { data } = await util.request(api.hpvActivity + `/${this.data.id}/storeList`, parmas, 'get', 2)
    const result = data.data
    if (data.code === 0) {
      if(result.length){
        that.setData({
          shopitem: result[0]
        })
      }
    } else {
      util.showToast({
        icon: 'none',
        title: data.msg
      })
    }
  },
  call(e) {
    wx.makePhoneCall({
      phoneNumber: e.currentTarget.dataset.phone
    })
  },
})