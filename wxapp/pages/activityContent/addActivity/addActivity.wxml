<!--pages/addPeople/addPeople.wxml-->
<view class="container">
	<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>

	<view class="flex_m wrap container-top">
		<image src="{{dataItem.imgUrlList[0]}}" mode="aspectFill" class="img"/>
		<text class="text-name"> {{dataItem.title}}</text>
	</view>
	<view class="w100 bg-color-white p30 mt20">
		<view class="flex_lr_m">
			<view class="f32 b c333">选择门店</view>
			<navigator open-type="navigate" hover-class="none" url="/pages/shopList/shopList?id={{dataItem.id}}&source=2&shopId={{shopitem.id}}">
				<view class="flex_m">
					<view class="c999 f24 lh24 ">切换门店</view>
					<image src="{{imgObject.ic_my_more}}" class="moreRight"></image>
				</view>
			</navigator>
		</view>
		<view class="pt20">
			<view class="rel">
				<view class="c999 f32 lh44 " wx:if="{{ !shopitem || shopitem.id==''}}">当前地区暂无可用门店</view>
				<block wx:else>
					<view class="bg-color-white">
						<view class="lh44 f32 shop-title mt8">{{shopitem.name}}</view>
						<view class="flex_lr_m mt8">
							<view>
								<view class="flex_wrap mt8">
									<image src="../../../static/images/home/<USER>" class="shop-icon"></image>
									<text lines="1" class="c666 f24 lh36">营业时间：{{shopitem.businessTime}}</text>
								</view>
								<view class="flex_wrap">
									<image src="../../../static/images/home/<USER>" class="shop-icon shop-loction-icon"></image>
									<text lines="1" class="c666 f24 lh36 shop-address">{{shopitem.address}}</text>
								</view>
							</view>
							<view class="shop-phone-box flex_c_m" wx:if="{{shopitem.contactsPhone}}" data-phone="{{shopitem.contactsPhone}}" bindtap="call">
								<image src="../../../static/images/home/<USER>" class="shop-phone-icon"></image>
							</view>
						</view>
					</view>
				</block>
			</view>
		</view>
	</view>
	<view class="w100 bg-color-white p30 mt20">
		<text class="f32 b c333">预约信息</text>
		<view class="item w100 bb1 flex_lr_m">
			<view class="c666">姓名</view>
			<input type="text" class="pt30 pb30 f32 c333 textright" value="{{checkerName}}" bindinput='inputChange' data-type="checkerName"
				placeholder-style="color:#B4B4B4" placeholder="请输入真实姓名" />
		</view>
		<view class="item w100 bb1 flex_lr_m">
			<view class="c666">身份证号</view>
			<input type="idcard" class="pt30 pb30 f32 c333 textright" bindinput="idCardChange" data-type="checkerIdCard" placeholder-style="color:#B4B4B4" maxlength="18"
				placeholder="请输入身份证号" />
		</view>
		<view class="item w100 bb1 flex_lr_m">
			<view class="c666">手机号码</view>
			<input class="pt30 pb30 f32 c333 textright" type="number" bindblur="inputChange" data-type="checkerPhone"
				placeholder-style="color:#B4B4B4" maxlength="11" placeholder="请输入手机号码" />
		</view>
		<view class="item w100 flex_lr_m">
			<view class="c666 pt30 w230">预约日期</view>
			<view class="rel pt30 flex_wrap check-time" bindtap="onDisplay">
				<view class="cB4 f32 lh44 textright" wx:if="{{!checkTime}}">请选择预约日期</view>
				<view class="c333 f32 lh44 textright" wx:else>{{checkTime}}</view>
				<image src="{{imgObject.ic_more_grey}}" class="noIcon"></image>
			</view>
			<van-calendar show="{{ show }}" bind:close="onClose" bind:select="onConfirm" formatter="{{ formatter }}" color="#367DFF" title="选择预约日期" :style="{ height: '400px' }" show-confirm="{{ false }}"/>
		</view>
		
	</view>
</view>
<view class="fixed b0 l0 w100 bg-color-white pl30 pr30 confir" wx:if="{{type === 1}}">
	<button bindtap="submitOrder" disabled="{{!checkerName || !checkerIdCard || !checkerPhone || !checkTime || !isCanOrder}}">提交预约</button>
</view>