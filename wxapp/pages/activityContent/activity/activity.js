// pages/people/people.js
var api = require('../../../config/api.js')
const util = require('../../../utils/util')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list: [],
    imgObject: {
      ic_address_edit: api.ImgUrl + 'images/ic_address_edit.png',
      ic_address_deleted: api.ImgUrl + 'images/ic_address_deleted.png',
      nomes: api.ImgUrl + 'images/nomes.png'
    },
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '活动专区',
  },
  // 活动详情
  activityDetail: function(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/activityContent/detail/detail?id=${id}`
    })
  },
  goback() {
    const { routerPage } = this.data
    setTimeout(() => {
      if (routerPage > 1) {
        wx.navigateBack({
          delta: 1
        })
      } else {
        wx.switchTab({
          url: '/pages/home/<USER>'
        })
      }
    }, 1000)
  },
  // 活动预约列表
  hpvActivityList(type) {
    const that = this
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    util.request(api.hpvActivityList, {
      cityId: app.globalData.cityId
    }, 'GET', 2)
    .then(res => {
      const result = res.data.data
      if (res.data.code === 0) {
        that.setData({
          list:
            type === 1 ? result.result : that.data.list.concat(result.result),
          loadComplete: result.hasNext
        })
        util.hideToast()
      } else {
        util.showToast({
          title: res.data.msg
        })
      }
    })
    util.hideLoading()
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.hpvActivityList(1)
  },

  onReachBottom: function() {
    if (this.data.loadComplete) {
      this.setData({
        ['listQuery.page']: ++this.data.listQuery.page
      })
      this.hpvActivityList(2)
    }
  },

})
