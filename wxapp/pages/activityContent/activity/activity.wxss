/* pages/people/people.wxss */
page{
  background: #F8F8F8;
}
.item{
	border-radius: 8rpx;
  padding-bottom: 28rpx;
}
.list .item {
  margin-bottom: 20rpx;
}
.m20 {
  margin: 20rpx 20rpx 0 20rpx;
}
.list .item .name {
  color: #333;
  font: 500 32rpx/44rpx 'PingFangSC, PingFang SC';
  padding-left: 28rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; 
  overflow: hidden;
  
}

.item image {
  display: inline-block;
  width: 100%;
  height: 300rpx;
  vertical-align: top;
  border-top-left-radius: 8rpx;
  border-top-right-radius: 8rpx;
  margin-bottom: 28rpx;
}
