// pages/confirmOrder/confirmOrder.js
const api = require('../../config/api.js')
const util = require('../../utils/util')
Page({

  /**
	 * 页面的初始数据
	 */
  data: {
    type: null, //1. 处方详情进入提交订单  2.处方信息进入提交订单 暂时不用
    recommendId: null, //处方推荐ID
    noAddress: false, //判断当前是否有地址
    imgObject: {
      ic_address: api.ImgUrl + 'images/ic_address.png',
      ic_more_black: api.ImgUrl + 'images/ic_more_black.png',
      img_order_bar: api.ImgUrl + 'images/img_order_bar.png'
    },
    address: null,
    addList: [],
    info: {},
    note: '',
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '确认订单',
    templateId: [],
    shippingInfoId: null,
    errMessage: '',
    overlay: false,
    isInvoice: false,
    invoiceInfo: {},
    showPicker: false,
    currentType: 0,
    invoiceType: [{ name: '个人', type: 0 }, { name: '单位', type: 1 }],
    showPaymentMethod: false,
    columns: ['微信支付', '他人代付'],
    paymentName: '微信支付',
    actions: [
        {
            name: '微信支付',
        },
        {
            name: '他人代付',
        },
    ],
  },
  // 获取模板
  getTemplate() {
    util.request(api.templates + '?type=3')
      .then(res => {
        if (res.data.code === 0) {
          const arry = []
          for (var i = 0; i < res.data.data.length; i++) {
            arry.push(res.data.data[i].templateId)
          }
          this.setData({
            templateId: arry
          })
        }
      })
      .catch(res => {
      })
  },
  // 跳转到处方信息
  goRecipeInfo: function() {
    wx.navigateTo({
      url: '/pages/recipeInfo/recipeInfo'
    })
  },

  settlements() {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    var info = wx.getStorageSync('userInfo')
    util.request(api.settlements + '?recomId=' + this.data.recommendId + '&userId=' + info.userId)
      .then(res => {
        util.hideToast()
        if (res.data.code == 0) {
          this.setData({
            info: res.data.data
          })
        } else {
          util.showToast({
            'title': res.data.msg
          })
        }
      })
      .catch(res => {
      })
  },
  orderCreate() {
    console.log(this.data.info.shippingInfo,"INfo")
    if (this.data.info && !this.data.info?.shippingInfo?.shippingInfoId) {
      wx.showToast({
        title: '请选择收货地址',
        icon: 'none', // 图标，支持 "success"、"loading"、"none"
        duration: 1500 // 提示框停留时间，单位为ms，默认值为1500ms
      })
      return false
    }
    util.showLoading({
      title: '提交中',
      mask: true
    })
    const { invoiceInfo, currentType, isInvoice } = this.data
    const invoice = {
      title: invoiceInfo.title,
      titleType: currentType,
      taxNo: invoiceInfo.taxNo,
      isInvoice: isInvoice ? 1 : 0
    }
    var data = {
      'payType': this.data.paymentName == '微信支付' ? 1 : 3,
      'remark': this.data.note,
      'shippingInfoId': this.data.info.shippingInfo.shippingInfoId,
      'totalAmount': util.accMul(this.data.info.price.realAmount, 100),
      'invoice': this.data.info.invoice,
      invoice
    }
    util.request(api.orderCreate, data, 'post', 1)
      .then(res => {
        if (res.data.code == 0) {
          // 若支付方式选择微信支付，点击提交订单，则调起支付控件，若支付方式选择他人代付，则跳转到代付详情
          if (this.data.paymentName == '微信支付') {
            //0元订单状态不调用支付控件，下单之后是待审核状态跳转订单详情
            if(res.data.data.realPay !== 0) {
              this.orderPay(res.data.data.orderId)
            } else {
              wx.redirectTo({
                url: '/pages/orderDetail/orderDetail?orderId=' + res.data.data.orderId
              })
            }
          } else {
            // 跳转他人代付详情
            wx.navigateTo({ url: `/pages/paymentDetail/index?orderId=${res.data.data.orderId}` })
          }
        } else {
          this.setData({
            overlay: true,
            errMessage: res.data.msg
          }, () => {
            setTimeout(() => {
              this.setData({
                overlay: false
              })
            }, 2000)
          })
        }
        util.hideLoading()
      })
      .catch(res => {
      })
  },
  authSub(orderNumber) {
    var that = this
    wx.requestSubscribeMessage({
      tmplIds: that.data.templateId,
      success: (res) => {
        that.paySuccess(orderNumber)
      },
      fail(res) {
        that.paySuccess(orderNumber)
      }
    })
  },
  async orderPay(orderNumber) {
    try {
      const {
        data
      } = await util.request(util.getRealUrl(api.orderPay, orderNumber))
      if (data.code !== 0) {
        throw new Error(data.msg)
      }
      wx.requestPayment({
        ...data.data,
        success: (result) => {
          this.authSub(orderNumber)
        },
        fail: () => {
          util.showModal({
            title: '支付结果',
            content: `您已取消支付，点击查看订单详情！`,
            showCancel: false,
            cancelText: '我点错了',
            cancelColor: '#666666',
            confirmText: '确定',
            success: (result) => {
              if (result.confirm) {
                wx.redirectTo({
                  url: '/pages/orderDetail/orderDetail?orderId=' + orderNumber
                })
              }
            }
          })

        }
      })
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
    util.hideLoading()
  },
  paySuccess(orderNumber) {
    wx.redirectTo({
      url: '/pages/paySuccess/paySuccess?id=' + orderNumber + '&type=1'
    })
  },
  // 订单备注
  changeNote(e) {
    this.setData({
      note: e.detail.value
    })
  },
  // 获取地址列表
  async getAddressList() {
    var that = this
    try {
      var {
        data
      } = await util.request(api.addresesList, {
        userAddress: 1
      }, 'GET')
      if (data.code !== 0) {
        that.setData({
          ['info.shippingInfo']: null,
          shippingInfoId: null
        })
        util.showToast({
          title: data.msg,
          icon: 'none'
        })
        return false
      }
      var id = that.data.shippingInfoId
      var list = data.data
      const flag = list.some((item) => item.id === id || item.defaultAddr === 1)
      if (!flag) {
        that.setData({
          ['info.shippingInfo']: null,
          shippingInfoId: null
        })
      } else {
        const chooseData = list.find((item) => item.id === id)
        const defaultData = list.find((item) => item.defaultAddr === 1)
        const newData = chooseData ? chooseData : defaultData
        const data = {
          shippingInfoId: newData.id,
          fullAddress: newData.province + newData.city + newData.county + newData.addr,
          receiver: newData.receiver,
          phone: newData.phone
        }
        that.setData({
          ['info.shippingInfo']: data
        })
      }
    } catch (error) {
      util.showToast({
        title: error.msg,
        icon: 'none'
      })
    }
  },

  // 获取购物车详情
  getSettlements(skuId, quantity) {
    const params = {}
    if (this.data.info.shippingInfo) {
      params.shippingInfoId = this.data.info.shippingInfo.shippingInfoId
    }
    if (skuId) {
      params.skuId = skuId
      params.quantity = quantity
    }
    util.request(api.cartsSettlements, params, 'get').then(res => {
      if (res.data.code == 0) {
        this.setData({
          info: res.data.data
        })
      } else {
        util.showToast({
          'title': res.data.msg
        })
      }
    })
  },

  /**
	 * 生命周期函数--监听页面加载
	 */
  onLoad: function(options) {
    this.setData({
      type: options.type,
      recommendId: options.id
    })
    // this.orderPay()
    // 来源是处方
    if(options.source === '1') {
      this.settlements()
      this.getTemplate()
    } else if(options.source === '2' || options.source === '3') {
      // 来源商品，购物车
      this.getSettlements(options.skuId, options.quantity)
    }
  },

  /**
	 * 生命周期函数--监听页面初次渲染完成
	 */
  onReady: function() {

  },
  onisInvoiceChange(e) {
    this.setData({
      isInvoice: e.detail,
      showPicker:e.detail
    })
    console.log(e, 'onisInvoiceChange')
  },
  handleOpenPacker() {
    this.setData({
      showPicker: !this.data.showPicker
    })
  },
  haneleChange(e) {
    const { type } = e.currentTarget.dataset
    this.setData({
      currentType: type
    })
  },
  onInputVal(e) {
    const { value } = e.detail
    const {
      key
    } = e.currentTarget.dataset
    this.setData({
      [`invoiceInfo.${key}`]: value
    })
    console.log(this.data.invoiceInfo, 255)
  },
  confirFun() {
    const { invoiceInfo, currentType, isInvoice } = this.data
    if (currentType == 0 && !invoiceInfo.title) {
      util.showToast({
        title: '请输入个人抬头',
        icon: 'none'
      })
      return
    }
    if (currentType == 1 && !invoiceInfo.title) {
      util.showToast({
        title: '请输入单位抬头',
        icon: 'none'
      })
      return
    }
    if (currentType == 1 && !invoiceInfo.taxNo) {
      util.showToast({
        title: '请输入纳税人识别号',
        icon: 'none'
      })
      return
    }
    const invoice = {
      title: invoiceInfo.title,
      titleType: currentType,
      taxNo: invoiceInfo.taxNo,
      isInvoice: isInvoice ? 1 : 0
    }
    if (invoice.titleType == 0) {
      delete invoice.taxNo
    }
    this.setData({
      'info.invoice': invoice,
      showPicker: false
    })
    console.log(this.data.info)
  },
  close() {
    const {invoiceInfo} = this.data
    if(!invoiceInfo.title&&!invoiceInfo.taxNo){
      this.setData({
        isInvoice: false
      })
    }
    this.setData({
      showPicker: false
    })
  },

  // 选中支付方式
  choosePayment() {
    this.setData({
        showPaymentMethod: true
    })
  },
// 选择支付方式
  onSelect(event) {
      this.setData({
          showPaymentMethod: false,
          paymentName: event.detail.name
      })
  },
  // 关闭支付方式
  onClose() {
      this.setData({
          showPaymentMethod: false
      })
  },


  /**
	 * 生命周期函数--监听页面显示
	 */
  onShow: function() {
    this.getAddressList()
  },

  /**
	 * 生命周期函数--监听页面隐藏
	 */
  onHide: function() {

  },

  /**
	 * 生命周期函数--监听页面卸载
	 */
  onUnload: function() {

  },

  /**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
  onPullDownRefresh: function() {

  },

  /**
	 * 页面上拉触底事件的处理函数
	 */
  onReachBottom: function() {

  }

  /**
	 * 用户点击右上角分享
	 */
  // onShareAppMessage: function() {

  // }
})
