<!--pages/confirmOrder/confirmOrder.wxml-->
<view class="bg-color-gray-light" style="padding-bottom: 140rpx;">
	<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
	<view class="tips">温馨提示：“根据《药品经营质量管理规范》第三章 第一百七十七条规定，除药品质量原因外，药品一经售出，不得退换”。</view>
	<view class="bg-color-white">
		<navigator open-type="navigate" class="pt40 pb40 pl30 pr30 rel flex_m" hover-class="none"
			url="/pages/address/index?type=2">
			<image style="width:44rpx;height:44rpx" src="{{imgObject.ic_address}}"></image>
			<view wx:if="{{info.shippingInfo!=null}}" class="flex_m">
				<view class="flex1 ml20">
					<view class="f32 c333">{{info.shippingInfo.receiver}} <text class="ml30">{{info.shippingInfo.phone}}</text>
					</view>
					<view class="f28 c333 mt10">{{info.shippingInfo.fullAddress}}</view>
				</view>
			</view>
			<view class="flex_line_c_m flex1" wx:else>
				<view class="f32 c333 b">请选择收货地址</view>
			</view>
			<image style="width:44rpx;height:44rpx;right:30rpx" class="abs" src="{{imgObject.ic_more_black}}"></image>
		</navigator>
	</view>
	<image class='w100' style="height:4rpx; display: block;" src="{{imgObject.img_order_bar}}"></image>
	<view class="p30 mt20 bg-color-white">
		<view class="f28 b">商品信息</view>
		<view class="mt30 bb1">
			<view wx:for="{{info.products}}" wx:key="index" class="flex_m mb20">
				<view class="drug_cover">
					<image src="{{item.icon}}" mode="aspectFill"></image>
				</view>
				<view class="flex_lr ml10 flex1">
					<view class="f28 c333 ml10 flex1">
						<text wx:if="{{item.rx === 1}}" class="tag">Rx</text>{{item.name}}
					</view>
					<view class="flex_tb">
						<view class="f28 c333 flex_c_end">
							¥{{item.salePrice}}
						</view>
						<view class="f28 c999 flex_c_end">
							x{{item.quantity}}
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="pt30 tr f28 c333 b">
			小计：<text>¥{{info.price.totalAmount}}</text>
		</view>
	</view>
	<view class="mt20 bg-color-white p30">
		<view class="pb30 f28 c999">配送方式 <text class="fr c333 f28">{{info.shippingMethod}}</text></view>
		<!-- <view class="pb30 f28 c999">支付方式 <text class="fr c333 f28">微信支付</text></view> -->
		<view class="pb30 f28 c999">支付方式 <text class="fr c333 f28" bindtap="choosePayment">{{paymentName}}></text></view>
		<view class="pb30 f28 c999 flex_lr_m">开具发票 <van-switch size="24px" checked="{{ isInvoice }}" bind:change="onisInvoiceChange" /></view>
		<view class="pb30 f28 c999 flex_lr_m" catchtap="handleOpenPacker" wx:if="{{isInvoice}}">发票抬头信息 <view class="c999 f28 flex_m">查看 <text class="rigth-icon "></text></view>
	</view>
	</view>
	<view class="mt20 bg-color-white p30">
		<view class="pb30 f28 c999">商品金额 <text class="fr c333 f28">￥{{info.price.totalAmount}}</text></view>
		<view class="pb30 f28 c999">运费 <text wx:if="{{info.price.freight>0}}" class="fr c333 f28">￥{{info.price.freight}}</text><text wx:else class="fr c333 f28">包邮</text></view>
        <view wx:if="{{info.price.tips}}" class="mb30 p10 f24 color-ff0000 bg-color-gray-light container-radius">{{info.price.tips}}</view>
        <view class="bb1"></view>
		<view class="pt30 tr f28">
			总计：<text class="color-danger b">¥{{info.price.realAmount}}</text>
		</view>
	</view>
	<view class="mt20 bg-color-white p30">
		<view class="mb20 f28 b">订单备注</view>
		<textarea class="remark p20 box_bb f28 container-radius bg-color-gray-light" placeholder="请输入备注信息…"
			placeholder-class="c999" bindinput="changeNote"></textarea>
	</view>
	<!-- <view class="p30 f28 c666 lh40">温馨提示：“根据《药品经营质量管理规范》第三章 第一百七十七条规定，除药品质量原因外，药品一经售出，不得退换”。</view> -->
	<view class="fixed b0 l0 w100 bg-color-white pl30 pr30 confir zx999 flex_lr_m" wx:if="{{!showPicker}}">
		<view class="flex_m c333 f24">实付款：<text class="f36 b color-danger">￥{{info.price.realAmount}}</text>
		</view>
		<view class="fixedbutton f26 container-radius" bindtap="orderCreate" disabled="{{info.shippingInfo===null}}">提交订单
		</view>
	</view>
</view>

<van-popup show="{{showPicker}}" bind:close='close' close-on-click-overlay='{{true}}' safe-area-inset-bottom="{{true}} "round='{{true}}' position="center"  closeable>
	<view class="content m30" catchtap="return">
		<view class="tc f32 c333 h40">
			纸质发票抬头
		</view>
		<view class="invoice_type flex_m">
			<view class="invoice_type_item {{currentType == item.type ?'invoice_type_active':''}}" data-type="{{item.type}}" bind:tap="haneleChange" wx:for="{{invoiceType}}">
				{{item.name}}
			</view>
		</view>
		<div wx:if="{{currentType==0}}">
			<view class="mt60 f24 c666 h40">
				个人抬头
			</view>
			<input class="invoice_input bb1" type="text" clearable placeholder="请输入个人抬头" value="{{invoiceInfo.title}}" placeholder-class="c999 f28" data-key='title' bind:input='onInputVal' />
		</div>
		<div wx:if="{{currentType==1}}">
			<view class="mt60 f24 c666 h40">
				单位抬头
			</view>
			<input class="invoice_input bb1" type="text" clearable placeholder="请输入单位抬头"  value="{{invoiceInfo.title}}" placeholder-class="c999 f28" data-key='title' bind:input='onInputVal' />
			<view class="mt30 f24 c666 h40" tyle="position: absolute;">
				纳税人识别号
			</view>
			<input class="invoice_input bb1" type="text" clearable placeholder="请输入纳税人识别号"  value="{{invoiceInfo.taxNo}}" placeholder-class="c999 f28" data-key='taxNo' bind:input='onInputVal' />
		</div>

		<view class="confir footer-btn">
			<button bindtap="confirFun">确定</button>
		</view>
	</view>
</van-popup>
<van-action-sheet
    z-index="9999"
    round='{{true}}'
    show="{{ showPaymentMethod }}"
    safe-area-inset-bottom="{{ true }}"
    actions="{{ actions }}"
    bind:close="onClose"
    bind:select="onSelect"
/>

<view wx:if='{{overlay}}' class="wrapper-overlay">
	{{errMessage}}
</view>
