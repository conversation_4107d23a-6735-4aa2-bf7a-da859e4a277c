// pages/famousDoctor/famousDoctor.js
var api = require('../../config/api')
const util = require('../../utils/util')
const app = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentCity: '',
    imgObject: {
      search: api.ImgUrl + 'images/<EMAIL>', //搜索图标
    },
    id: null,
    shopId: null,
    name: '',
    keyWord: '',
    list: [],
    listQuery: {
      page: 1, // 页码
    },
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '门店列表',
    keyWord: '',
    source: 1
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.setData({
      id: +options.id || null,
      source: +options.source || 1,
      shopId: +options.shopId || null,
      currentCity: app.globalData.city
    })
  },
  onShow: function () {
    this.getShopList(1)
  },
  goCityList() {
    wx.navigateTo({
      url: `/pages/cityList/cityList?currentCity=${this.data.currentCity}`
    })
  },
  call(e) {
    wx.makePhoneCall({
      phoneNumber: e.currentTarget.dataset.phone
    })
  },
  // 选择门店
  chooseShop(e) {
    const id = e.currentTarget.dataset.id
    const shopitem = e.currentTarget.dataset.shopitem
    console.log('shopitem', shopitem)
    var pages = getCurrentPages()
    var prevPage = pages[pages.length - 2]
    console.log('prevPage', prevPage)
    prevPage.setData({
      shopitem: shopitem,
      orderTimeList: shopitem.orderTimeList
    })
    wx.navigateBack({
      delta: 1
    })
  },
  changeKeyWord(e) {
    this.setData({
      keyWord: util.filterEmoji(e.detail.value)
    })
  },
  changeSearch(e) {
    if (this.data.keyWord !== '') {
      this.getShopList(1)
    }
  },
  onReachBottom: function() {
    if (this.data.loadComplete) {
      this.setData({
        ['listQuery.page']: ++this.data.listQuery.page
      })
      this.getShopList(2)
    }

  },
  async getShopList(type) { //type=1 直接请求 =2分页请求
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    var that = this
    var parmas = {
      page: type === 1 ? 1 : that.data.listQuery.page,
      name: that.data.keyWord,
      cityId: app.globalData.cityId,
    }
    let url = this.data.source ===1 ? api.hpvProduct : api.hpvActivity
    const { data } = await util.request(url + `/${this.data.id}/storeList`, parmas, 'get', 2)
    util.hideToast()
    const result = data.data
    if (data.code === 0) {
      that.setData({
        list: type === 1 ? result : that.data.list.concat(result),
        loadComplete: !result.hasNext ? false : true
      })
    } else {
      util.showToast({
        icon: 'none',
        title: data.msg
      })
    }
  },
  goCheck(){

  }
  /**
   * 获取常见疾病
   */
})
