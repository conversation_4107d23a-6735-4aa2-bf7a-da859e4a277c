.page {
  background-color: rgba(255,255,255,1);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.search .city-box {
  /* position: absolute;
  left: 20rpx;
  top: 18rpx; */
  padding:0 20rpx;
  border-right: 2rpx solid #dfdfdf;
}
.search .city-title{
  font-size: 28rpx;
  line-height: 28rpx;
}
.city-image{
  width: 18rpx;
  height: 18rpx;
  margin-left: 12rpx;
}
.search .rel {
  background:#F8F8F8;
  border: 2rpx solid #dfdfdf;
  height: 80rpx;
  border-radius: var(--btnRadius);
}

.search .rel input {
  display: block;
  width: 100%;
  height: 100%;
  line-height: 80rpx;
  padding-left: 14rpx;
  box-sizing: border-box;
}
.search .search-box{
  padding-left: 18rpx;
}
.search .icon {
  width: 40rpx;
  height: 40rpx;
  /* position: absolute;
  left: 184rpx;
  top: 19rpx; */
}

.text-title{
  overflow-wrap: break-word;
  color: rgba(43,40,39,1);
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
}
.list-items-checked {
  background: #F8FAFF;
  border: 0.5px solid rgba(197,218,255,1);
}
.mt8{
  margin-top: 8rpx;
}
.mb8{
  margin-bottom: 8rpx;
}
.shop-icon{
  width: 24rpx;
  height: 24rpx;
  margin-right: 12rpx;
}
.hop-loction-icon{
  width: 22rpx;
  height: 26rpx;
}
.phone-icon {
  width: 56rpx;
  height: 56rpx;
  margin-right: 28rpx;
}
.flex_wrap{
  align-items: center;
}
.shop-phone-box{
  width: 56rpx;
  height: 56rpx;
  background: #F8F8F8;
  border-radius: 18rpx;
}
.shop-phone-icon{
  width: 28rpx;
  height: 28rpx;
}
.item-wrap {
  position: relative;
}
.choice-bg{
  background: #F8FAFF;
  border: 1rpx solid #C5DAFF;
}
.choice-img{
  position: absolute;
  right: 0;
  bottom: 0;
  width: 40rpx;
  height: 40rpx;
}
.br8{
  border-radius: 8rpx;
}