<!--pages/famousDoctor/famousDoctor.wxml-->
<view class="container bg-color-gray-light">
	<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
	<view class="w100e">
		<view class="p20 rel search bg-color-white">
			<view class="w100 rel flex_m">
				<view class="city-box flex_m" bindtap="goCityList">
					<text class="city-title c333 f28">{{currentCity}}</text>
					<image src="../../static/images/home/<USER>" class="city-image"></image>
					<!-- <view class="icon">
						<image src="{{imgObject.search}}" class="imgBlock"></image>
					</view> -->
				</view>
				<view class="flex_c search-box">
					<view class="icon">
						<image src="{{imgObject.search}}" class="imgBlock"></image>
					</view>
					<input type="text" class="w100"  bindconfirm='changeSearch' value="{{keyWord}}" bindinput='changeKeyWord' placeholder="搜索当前城市门店" placeholder-class="c999" class="f28 c999" />
				</view>
			</view>
		</view>
		<view class="pl20 pr20 mt20">
			<view wx:for="{{list}}" class="br8 bg-color-white p28 mb20 item-wrap {{shopId === item.id? 'choice-bg': ''}}" data-id='{{item.id}}' wx:key="id" bindtap="chooseShop" data-shopitem="{{ item }}">
				<vew class="lh44 f32 text-title mt8">{{item.name}}</vew>
				<view class="flex_lr_m mt8">
					<view>
						<view class="flex_wrap mt8">
							<image src="../../static/images/home/<USER>" class="shop-icon"></image>
							<text lines="1" class="c666 f24 lh36">营业时间：{{item.businessTime}}</text>
						</view>
						<view class="flex_wrap">
							<image src="../../static/images/home/<USER>" class="shop-icon shop-loction-icon"></image>
							<text lines="1" class="c666 f24 lh36">{{item.address}}</text>
						</view>
					</view>
					<view class="shop-phone-box flex_c_m" wx:if="{{item.contactsPhone}}" data-phone="{{item.contactsPhone}}" catchtap="call">
						<image src="../../static/images/home/<USER>" class="shop-phone-icon"></image>
					</view>
				</view>
				<image src="../../static/images/home/<USER>" class="choice-img" wx:if="{{shopId === item.id}}"/>
			</view>
		</view>
	</view>
</view>
