// pages/addPeople/addPeople.js
const api = require('../../../config/api')
const util = require('../../../utils/util')
const check = require('../../../utils/check')
const app = getApp()
Page({
  ...check,
  /**
   * 页面的初始数据
   */
  data: {
    imgObject: {
      ic_upload_add: api.ImgUrl + 'images/ic_upload_add.png',
      ic_upload_delect: api.ImgUrl + 'images/ic_upload_delect.png',
      ic_more_black: api.ImgUrl + 'images/ic_more_black.png',
      ic_more_grey: api.ImgUrl + 'images/ic_more_grey.png',
      ic_my_more: api.ImgUrl + 'images/ic_my_more.png',
    },
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '提交订单',
    info: {
      inquirerId: '',
    },
    peopleInfo: {}, //所有就诊人ID
    dataItem: {},
    shopitem: {
      id: ''
    },
    checkTime: '',
    show: false,
    orderTimeList: [],
    selectedDates: [],
    formatter(day) {
      return day
    }
  },

  hpvProduct() {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    util.request(api.hpvProduct + `/${this.data.id}`, {
      cityId: app.globalData.cityId
    }, 'GET', 2)
    .then(res => {
      if (res.data.code === 0) {
        this.setData({
          dataItem: res.data.data,
          shopitem: res.data.data.storeVO,
          orderTimeList: res.data.data.storeVO?.orderTimeList || []
        })
        util.hideToast()
      } else {
        util.showToast({
          title: res.data.msg
        })
      }
    })
    // var data = await util.hpvProductList()
    util.hideLoading()
  },
  async getInquirerList() {
    var id = this.data.info.inquirerId
    var list = await util.getPeopleList()
    if (id) {
      for (let i = 0; i < list.length; i++) {
        if (list[i].inquirerId === id) {
          list[i].phone = util.stringHidden(list[i].phone, 3, 4)
          list[i].guardianPhone = util.stringHidden(list[i].guardianPhone, 3, 4)
          this.setData({
            peopleInfo: list[i],
            ['info.inquirerId']: id
          })
          break
        }
      }
    } else {
      for (let i = 0; i < list.length; i++) {
        if (list[i].idCard && list[i].relation === 0) {
          list[i].phone = util.stringHidden(list[i].phone, 3, 4)
          this.setData({
            peopleInfo: list[i],
            ['info.inquirerId']: list[i].inquirerId
          })
          break
        }
      }
    }
  },
  async getShopList() { //type=1 直接请求 =2分页请求
    var that = this
    var parmas = {
      page: 1,
      cityId: app.globalData.cityId,
    }
    const { data } = await util.request(api.hpvProduct + `/${this.data.id}/storeList`, parmas, 'get', 2)
    const result = data.data
    if (data.code === 0) {
      if(result.length){
        that.setData({
          shopitem: result[0]
        })
      }
    } else {
      util.showToast({
        icon: 'none',
        title: data.msg
      })
    }
  },
  call(e) {
    wx.makePhoneCall({
      phoneNumber: e.currentTarget.dataset.phone
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    this.setData({
      id: options.id * 1,
      ['info.inquirerId']: options.inquirerId,
    })
    await this.hpvProduct()
    await this.getInquirerList() // 获取就诊人
    // await this.getShopList()
    await this.formatterFuc()
  },
  onShow: async function() {
    if(this.data.checkTime){
      this.setData({
        checkTime: ''
      })
    }
    this.getInquirerList() // 获取就诊人
  },

  onDisplay() {
    console.log('open')
    this.formatterFuc()
    this.setData({ show: true });
  },
  onClose() {
    this.setData({ show: false });
  },
  formatterFuc() {
    console.log('checkTime', this.data.checkTime)
    console.log('orderTimeList2', this.data.orderTimeList)
    const that = this
    const checkTime = this.data.checkTime
    this.setData({
      formatter: function(day) {
        const date = day.date;
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const dayNum = date.getDate();
        const dayStr = `${year}-${month < 10 ? '0' + month : month}-${dayNum < 10 ? '0' + dayNum : dayNum}`;
        const isAvailable = that.data.orderTimeList.includes(dayStr);
        day.bottomInfo = isAvailable ? '可约' : '不可约'
        if (day.type === 'selected' && checkTime) {
          return day;
        }
        day.type = isAvailable ? '' : 'disabled'
        return day;
      },
      
    })
  },
  formatDate(date) {
    date = new Date(date);
    let month = (date.getMonth() + 1).toString().padStart(2, '0');
    let day = date.getDate().toString().padStart(2, '0');
    return `${date.getFullYear()}-${month}-${day}`;
  },
  onConfirm(event) {
    console.log('event', event)
    this.setData({
      show: false,
      checkTime: this.formatDate(event.detail),
    });
  },
  gotoServe(){
    wx.reLaunch({
      url: `/pages/serveContent/userServe/userServe`
    })
  },
  addServe(){
    const that = this
    const params = {
      storeId: this.data.shopitem.id,
      id: this.data.id,
      checkTime: this.data.checkTime,
      inquirerId: this.data.info.inquirerId
    }
    const sysInfo = wx.getSystemInfoSync()
    const tokenKey = wx.getStorageSync('tokenKey')
    const header = {
      'content-type': 'multipart/form-data; boundary=XXX'
    }
    const headerParams = {
      // '_p': 0,
      '_m': sysInfo.model,
      '_o': 0,
      '_w': 1
    }
    if (wx.getStorageSync('token')) {
      header[tokenKey] = wx.getStorageSync('token')
    }
     wx.request({
          url: api.hpvProduct + `/${this.data.id}/submitOrder`,
          method: 'POST',
          header: Object.assign(header, headerParams),
          data: '\r\n--XXX' +
                '\r\nContent-Disposition: form-data; name="storeId"' +
                '\r\n' +
                '\r\n' + params.storeId +
                '\r\n--XXX' +
                '\r\nContent-Disposition: form-data; name="id"' +
                '\r\n' +
                '\r\n' + params.id +
                '\r\n--XXX' +
                '\r\nContent-Disposition: form-data; name="checkTime"' +
                '\r\n' +
                '\r\n' + params.checkTime +
                '\r\n--XXX' +
                '\r\nContent-Disposition: form-data; name="inquirerId"' +
                '\r\n' +
                '\r\n' + params.inquirerId +
                '\r\n--XXX--'
          , 
          success: function (res) {
              var wxPayData = res.data.data
              wx.requestPayment({
                'timeStamp': wxPayData.timeStamp,
                'nonceStr': wxPayData.nonceStr,
                'package': wxPayData.package,
                'signType': 'MD5',
                'paySign': wxPayData.paySign,
                'success': function(res) {
                  util.showToast({
                    title: '支付成功',
                    icon: 'success',
                    duration: 1000,
                    success: function() {
                      setTimeout(() => {
                        that.gotoServe()
                      }, 1000)
                    }
                  })
                },
                'fail': function(res) {
                  util.showToast({
                    title: '支付失败',
                    icon: 'none'
                  })
                },
                'complete': function(res) {}
              })
          }
        })
    //  util.request(api.hpvProduct + `/${this.data.id}/submitOrder`, data, 'POST', 6)
    //   .then(res => {
    //     console.log('res', res)
    //     if (res.data.code === 0) {
    //       // util.showToast({
    //       //   title: `提交预约成功`,
    //       //   icon: 'success'
    //       // })
    //       // wx.navigateBack({
    //       //   delta: 1
    //       // })
    //     }
    //   })
  }

})