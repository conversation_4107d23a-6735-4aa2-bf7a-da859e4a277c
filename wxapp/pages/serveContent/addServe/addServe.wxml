<!--pages/addPeople/addPeople.wxml-->
<view class="container">
	<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
	<view class="w100 bg-color-white p30">
		<view class="f32 b c333">预约人信息 <text class="rd">*</text></view>
		<navigator open-type="navigate" hover-class="none" url="/pages/peopleContent/people/people?source=1">
			<view class="pt40">
				<view class="rel">
					<view class="c999 f32 lh44 " wx:if="{{info.inquirerId==''}}">选择就诊人</view>
					<block wx:else>
						<view class="c333 b f36">{{peopleInfo.name}} <text
								class="pl30 pr30 n c666 f32">{{peopleInfo.gender===1?'男':'女'}} <text
									class="pl20">{{peopleInfo.age}}</text></text><text
								class="tag n  f24 ">{{peopleInfo.relationName}}</text></view>
						<view class="f28 c333 pt10">
							{{!peopleInfo.phone && !peopleInfo.guardianPhone ? '-':(peopleInfo.phone?peopleInfo.phone:peopleInfo.guardianPhone) }}
						</view>
					</block>
					<image src="{{imgObject.ic_more_black}}" class="noIcon"></image>
				</view>
			</view>
		</navigator>
	</view>
	<view class="w100 bg-color-white p30 mt20">
		<view class="f32 b c333">商品信息</view>
		<view class="flex_m serve-wrap-top mt20">
			<image src="{{dataItem.imgUrlList[0]}}" mode="aspectFill" class="img"/>
			<view>
				<text class="text-name"> {{dataItem.name}}</text>
				<text class="text-name red"> ¥ {{dataItem.price}}</text>
			</view>
		</view>
	</view>
	<view class="w100 bg-color-white p30 mt20">
		<view class="flex_lr_m">
			<view class="f32 b c333">选择门店</view>
			<navigator open-type="navigate" hover-class="none" url="/pages/shopList/shopList?id={{dataItem.id}}&source=1&shopId={{shopitem.id}}">
				<view class="flex_m">
					<view class="c999 f24 lh24 ">切换门店</view>
					<image src="{{imgObject.ic_my_more}}" class="moreRight"></image>
				</view>
			</navigator>
		</view>
		<view class="pt20">
			<view class="rel">
				<view class="c999 f32 lh44 " wx:if="{{!shopitem || shopitem.id==''}}">当前地区暂无可用门店</view>
				<block wx:else>
					<view class="bg-color-white">
						<view class="lh44 f32 shop-title mt8">{{shopitem.name}}</view>
						<view class="flex_lr_m mt8">
							<view>
								<view class="flex_wrap mt8">
									<image src="../../../static/images/home/<USER>" class="shop-icon"></image>
									<text lines="1" class="c666 f24 lh36">营业时间：{{shopitem.businessTime}}</text>
								</view>
								<view class="flex_wrap">
									<image src="../../../static/images/home/<USER>" class="shop-icon shop-loction-icon"></image>
									<text lines="1" class="c666 f24 lh36 shop-address">{{shopitem.address}}</text>
								</view>
							</view>
							<view class="shop-phone-box flex_c_m" wx:if="{{shopitem.contactsPhone}}" data-phone="{{shopitem.contactsPhone}}" bindtap="call">
								<image src="../../../static/images/home/<USER>" class="shop-phone-icon"></image>
							</view>
						</view>
					</view>
				</block>
			</view>
		</view>
	</view>
	<view class="w100 bg-color-white p30 mt20">
		<view class="f32 b c333">预约日期</view>
		<view class="pt40">
			<view class="rel" bindtap="onDisplay">
				<view class="cB4 f32 lh44 " wx:if="{{!checkTime}}">请选择预约日期</view>
				<view class="c333 f32 lh44" wx:else>{{checkTime}}</view>
				<image src="{{imgObject.ic_more_grey}}" class="noIcon"></image>
			</view>
			<van-calendar show="{{ show }}" bind:close="onClose" bind:select="onConfirm" formatter="{{ formatter }}" color="#367DFF" title="选择预约日期" :style="{ height: '400px' }" show-confirm="{{ false }}"/>
		</view>
	</view>
</view>

<view class="fixed b0 l0 w100 bg-color-white pl30 pr30 confir">
	<button bindtap="addServe" disabled="{{!checkTime || !shopitem.id || !info.inquirerId}}">提交订单</button>
</view>