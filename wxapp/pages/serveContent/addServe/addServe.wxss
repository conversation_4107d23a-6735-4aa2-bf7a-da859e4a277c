/* pages/addServe/addServe.wxss */
@import '../detail/detail.wxss';
.noIcon{
  width: 44rpx;
  height: 44rpx;
  position: absolute;
  right: 0;
  top: 50%;
  margin-top: -22rpx;
}
.moreRight{
  width: 20rpx;
  height: 44rpx;
}
.tag{
  background: #E7F4FF;
  border-radius: 4rpx;
  width: 76rpx;
  height:36rpx ;
  text-align: center;
  display: inline-block;
  vertical-align: middle;
  color: var(--themeColor);
}
.rd {
  margin-left: 8rpx;
  color: #ff5555;
}
.serve-wrap-top image{
  width: 148rpx;
  height: 148rpx;
}
.serve-wrap-top .text-name{
  color: #2B2827;
  font: 500 32rpx/44rpx 'PingFangSC, PingFang SC';
  padding-left: 28rpx;
  padding-right: 28rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; 
  overflow: hidden;
  width: 502rpx;
}
.serve-wrap-top .red{
  font: 500 34rpx/48rpx 'PingFangSC, PingFang SC';
  color: #F05542;
}
.mt8{
  margin-top: 8rpx;
}
.mb8{
  margin-bottom: 8rpx;
}
.shop-icon{
  width: 24rpx;
  height: 24rpx;
  margin-right: 12rpx;
}
.hop-loction-icon{
  width: 22rpx;
  height: 26rpx;
}
.phone-icon {
  width: 56rpx;
  height: 56rpx;
  margin-right: 28rpx;
}
.flex_wrap{
  align-items: center;
}
.shop-phone-box{
  width: 56rpx;
  height: 56rpx;
  background: #F8F8F8;
  border-radius: 18rpx;
}
.shop-phone-icon{
  width: 28rpx;
  height: 28rpx;
}
.shop-title {
  width: 100%;
  color: #333;
  font: 600 32rpx/44rpx 'PingFangSC, PingFang SC';
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.shop-address{
  width: 502rpx;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; 
}
.van-calendar__day--disabled .van-calendar__bottom-info{
  color: #999999;
}
.month-index--van-calendar__bottom-info{
  color: #367DFF;
}
.van-calendar__selected-day .month-index--van-calendar__bottom-info{
  color: #fff;
}