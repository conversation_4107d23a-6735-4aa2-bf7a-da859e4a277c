/* pages/people/people.wxss */
page{
  background: #F8F8F8;
}
.item{
  width:344rpx;
	border-radius: 8rpx;
  padding-bottom: 20rpx;
}
.list .item {
  margin-bottom: 20rpx;
}
.m20 {
  margin: 20rpx 20rpx 0 20rpx;
}
.ml20 {
  margin-left: 22rpx;
}
.m-l20{
  overflow: hidden;
  margin-left: -22rpx;
}
.fl {
  float: left;
}
.list .item .name {
  color: #333;
  font: 400 28rpx/40rpx 'PingFangSC, PingFang SC';
  padding-left: 20rpx;
  padding-right: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; 
  overflow: hidden;
  margin-bottom: 20rpx;
}
.list .item .money {
  color: #F05542;
  font: 500 32rpx/44rpx 'PingFangSC, PingFang SC';
  padding-left: 20rpx;  
}

.item image {
  display: inline-block;
  width: 100%;
  height: 344rpx;
  vertical-align: top;
  border-top-left-radius: 8rpx;
  border-top-right-radius: 8rpx;
  margin-bottom: 20rpx;
}
