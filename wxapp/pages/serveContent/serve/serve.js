// pages/people/people.js
var api = require('../../../config/api.js')
const util = require('../../../utils/util')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list: [],
    listQuery: {
      page: 1, // 页码
    },
    isBack: true,
    backgroundColor: '#fff',
    navTitle: 'HPV服务产品',
    loadComplete: null,
    
  },
  // 服务产品
  serveDetail: function(e) {
    const index = e.currentTarget.dataset.index
    const id = this.data.list[index].id
    wx.navigateTo({
      url: `/pages/serveContent/detail/detail?id=${id}`
    })
  },
 
  // 服务产品列表
  hpvProductList(type) {
    const that = this
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    util.request(api.hpvProductList, {
      cityId: app.globalData.cityId
    }, 'GET', 2, false)
    .then(res => {
      const result = res.data.data
      if (res.data.code === 0) {
        that.setData({
          list:
            type === 1 ? result.result : that.data.list.concat(result.result),
          loadComplete: result.hasNext
        })
        util.hideToast()
      } else {
        util.showToast({
          title: res.data.msg
        })
      }
    })
    util.hideLoading()
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.setData({
      type: options.type * 1,
      source: options.source * 1,
      doctorId: options.doctorId,
    })
  },
  onReachBottom: function() {
    if (this.data.loadComplete) {
      this.setData({
        ['listQuery.page']: ++this.data.listQuery.page
      })
      this.hpvProductList(2)
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    this.hpvProductList(1)
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  }

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage: function () {

  // }
})
