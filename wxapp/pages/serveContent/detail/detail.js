// pages/peopleContent/detail/detail.js
const check = require('../../../utils/check')
const util = require('../../../utils/util')
const api = require('../../../config/api')
const app = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    id: null,
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '商品详情',
    current: 1,
    dataItem: {
      imgList: ['https://img.zcool.cn/community/0101d75d709f3ca801202f177e96ba.gif','https://img.zcool.cn/community/0121e65c3d83bda8012090dbb6566c.jpg','https://img.zcool.cn/community/0101d75d709f3ca801202f177e96ba.gif'],
      name: 'HPV轮状疫苗免费接种-火热预约中HPV轮状疫苗免费接种-火热预约中',
      money: '0.01'
    },
  },
   // 切换产品图
  handleSwiperChange (e) {
    const n = e.detail.current
    this.setData({
      current: n + 1
    })
  },
  goback() {
    const { routerPage } = this.data
    setTimeout(() => {
      if (routerPage > 1) {
        wx.navigateBack({
          delta: 1
        })
      } else {
        wx.switchTab({
          url: '/pages/home/<USER>'
        })
      }
    }, 1000)
  },
  addServe(e){
    wx.navigateTo({
      url: `/pages/serveContent/addServe/addServe?id=${this.data.id}`
    })

  },
  hpvProduct() {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    util.request(api.hpvProduct + `/${this.data.id}`, {
      cityId: app.globalData.cityId
    }, 'GET', 2)
    .then(res => {
      if (res.data.code === 0) {
        // 富文本图片自适应
        res.data.data.content =  res.data.data.content.replace(/\<img/gi, '<img style="max-width:100%;height:auto;margin: 10px auto;" ')
        this.setData({
          dataItem: res.data.data,
        })
        util.hideToast()
      } else {
        util.showToast({
          title: res.data.msg
        })
      }
    })
    // var data = await util.hpvProductList()
    util.hideLoading()
  },
  onLoad: function(options) {
    this.setData({
      id: options.id * 1,
    })
  },
  onShow: function() {
    this.hpvProduct()
  }
})