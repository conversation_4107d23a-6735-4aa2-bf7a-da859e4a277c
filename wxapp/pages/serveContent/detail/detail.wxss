.container{
  background: #F8F8F8;
  padding-bottom: 174rpx;
}
.wrap{
  background: #FFF;
  margin-bottom: 20rpx;
}
.container-top {
  padding-bottom: 28rpx;
}
.name {
  color: #333;
  font: 400 32rpx/44rpx 'PingFangSC, PingFang SC';
  padding-left: 28rpx;
  padding-right: 28rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; 
  overflow: hidden;
  margin-bottom: 8rpx;
}
.money {
  color: #F05542;
  font: 500 34rpx/48rpx 'PingFangSC, PingFang SC';
  padding-left: 28rpx;
  margin-bottom: 28rpx;
}

swiper {
	height: 640rpx;
}
.banner-wrap image {
  display: block;
  width: 100%;
  height: 640rpx;
  vertical-align: top;
}
.banner-wrap {
  position: relative;
  margin-bottom: 28rpx;
}
.swiper-current{
  width: 72rpx;
  height: 40rpx;
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  background: rgba(39,39,39,0.09);
  color: #fff;
  font: 400 26rpx/40rpx 'PingFangSC, PingFang SC';
  text-align: center;
}
.title-wrap{
  align-items: center;
}
.title{
  color: #333;
  font: 500 32rpx/44rpx 'PingFangSC, PingFang SC';
}
.title-wrap image {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}
.container-middle{
  margin: auto 10rpx;
  padding: 28rpx auto;
}
.content-wrap img{
  width: 100%
}