<!--pages/addPeople/addPeople.wxml-->
<view class="container p20">
	<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
	<view class="flex_wrap mt8 mb30">
		<image src="../../../static/images/home/<USER>" class="completed-icon"wx:if="{{dataItem.payStatus === 2}}"></image>
		<image src="../../../static/images/home/<USER>" class="completed-icon" wx:else></image>
		<text lines="1" class="c333 f36 b lh36">{{payStatusMap[dataItem.payStatus]}}</text>
	</view>
	<view class="w100 bg-color-white p30">
		<view class="f32 b c333">预约人信息</view>
		<navigator open-type="navigate" hover-class="none" url="/pages/peopleContent/people/people?source=1">
			<view class="pt20">
				<view class="c666 f28">姓名：{{dataItem.inquirerName}} <text
						class="pl30 pr30 n c666 f28">{{dataItem.inquirerGender===1?'男':'女'}} <text
							class="pl20">{{dataItem.inquirerAge}}</text></text></view>
				<view class="f28 c666 pt10 f28">
					手机号：{{ dataItem.inquirerPhone }}
				</view>
			</view>
		</navigator>
	</view>
	<view class="w100 bg-color-white p30 mt20">
		<view class="f32 b c333">门店信息</view>
		<view class="pt20">
			<view class="bg-color-white">
				<view class="lh44 f32 text-title mt8 shop-title">{{dataItem.storeName}}</view>
				<view class="flex_lr_m mt8">
					<view>
						<view class="flex_wrap mt8">
							<image src="../../../static/images/home/<USER>" class="shop-icon"></image>
							<text lines="1" class="c666 f24 lh36">营业时间：{{dataItem.businessTime}}</text>
						</view>
						<view class="flex_wrap shop-address">
							<image src="../../../static/images/home/<USER>" class="shop-icon shop-loction-icon"></image>
							<text lines="1" class="c666 f24 lh36">{{dataItem.address}}</text>
						</view>
					</view>
					<view class="shop-phone-box flex_c_m" wx:if="{{dataItem.contactsPhone}}" data-phone="{{dataItem.contactsPhone}}" bindtap="call">
						<image src="../../../static/images/home/<USER>" class="shop-phone-icon"></image>
					</view>
				</view>
			</view>
		</view>
	</view>
	<view class="w100 bg-color-white p30 mt20">
		<view class="f32 b c333 lh32">商品信息</view>
		<view class="flex_m serve-wrap-top mt20 w100">
			<image src="{{dataItem.productImgUrl}}" mode="aspectFill" class="img"/>
			<view class="flex_lr_m">
				<text class="text-name "> {{dataItem.productName}}</text>
				<text class="f28 c333 lh40"> ¥ {{dataItem.price}}</text>
			</view>
		</view>
	</view>
	<view class="w100 bg-color-white p30 mt20">
		<view class="flex_lr_m mb20">
			<text class="text-title c666 f28">商品金额</text> <text class="text-hidden c333 f28 b"> ¥ {{dataItem.price}}</text>
		</view>
		<view class="flex_c_end">
			<text class="c666 f24 lh48">实付款：</text> <text class="text-hidden red f36"> ¥ {{dataItem.realPrice}}</text>
		</view>
	</view>
	<view class="w100 bg-color-white p30 mt20">
		<view>
			<view class="text-contet flex_lr_m mb20">
				<text class="text-title c666 f28">订单编号：</text> <text class="c333 f28">{{dataItem.orderSn}}</text>
			</view>
			<view class="text-contet flex_lr_m mb20">
				<text class="c666 f28">下单时间：</text> <text class="c333 f28"> {{dataItem.createdAt}}</text>
			</view>
			<view class="text-contet flex_lr_m">
				<text class="c666 f28">预约时间：</text> <text class="c333 f28"> {{dataItem.checkTime}}</text>
			</view>
		</view>
	</view>
</view>
