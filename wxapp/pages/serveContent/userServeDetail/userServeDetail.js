// pages/addPeople/addPeople.js
const api = require('../../../config/api')
const util = require('../../../utils/util')
const check = require('../../../utils/check')
const app = getApp()
Page({
  ...check,
  /**
   * 页面的初始数据
   */
  data: {
    id: null,
    imgObject: {
      ic_upload_add: api.ImgUrl + 'images/ic_upload_add.png',
      ic_upload_delect: api.ImgUrl + 'images/ic_upload_delect.png',
      ic_more_black: api.ImgUrl + 'images/ic_more_black.png',
      ic_more_grey: api.ImgUrl + 'images/ic_more_grey.png',
      ic_my_more: api.ImgUrl + 'images/ic_my_more.png',
    },
    isBack: true,
    backgroundColor: '',
    navTitle: '订单详情',
    dataItem: {},
    payStatusMap: {
      1: '待支付',
      2: '已支付',
      3: '支付失败',
      4: '支付中',
      5: '关闭订单',
      6: '已退款'
    }
  },

  hpvProduct() {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    util.request(api.hpvProduct + `/order/detail`, {
      id: this.data.id
    }, 'GET', 2)
    .then(res => {
      if (res.data.code === 0) {
        this.setData({
          dataItem: res.data.data
        })
        util.hideToast()
      } else {
        util.showToast({
          title: res.data.msg
        })
      }
    })
    // var data = await util.hpvProductList()
    util.hideLoading()
  },
  call(e) {
    wx.makePhoneCall({
      phoneNumber: e.currentTarget.dataset.phone
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    this.setData({
      id: options.id * 1
    })
    await this.hpvProduct()
  },
  onShow: async function() {
  },

})