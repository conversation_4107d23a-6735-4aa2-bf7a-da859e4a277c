/* pages/addServe/addServe.wxss */
@import '../detail/detail.wxss';
.container {
  background: #F8F8F8;
  background: linear-gradient( 180deg, #E8EFFC 0%, #FDFDFE 40%, #F8F8F8 100%);
}
.noIcon{
  width: 44rpx;
  height: 44rpx;
  position: absolute;
  right: 0;
  top: 50%;
  margin-top: -22rpx;
}
.moreRight{
  width: 20rpx;
  height: 44rpx;
}
.tag{
  background: #E7F4FF;
  border-radius: 4rpx;
  width: 76rpx;
  height:36rpx ;
  text-align: center;
  display: inline-block;
  vertical-align: middle;
  color: var(--themeColor);
}
.rd {
  margin-left: 8rpx;
  color: #ff5555;
}
.serve-wrap-top .img{
  width: 128rpx;
  height: 128rpx;
  border-radius: 10rpx;
}
.serve-wrap-top .text-name{
  color: #2B2827;
  font: 500 28rpx/40rpx 'PingFangSC, PingFang SC';
  padding-left: 28rpx;
  padding-right: 28rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; 
  overflow: hidden;
  width: 442rpx;
}
.red{
  font: 500 34rpx/48rpx 'PingFangSC, PingFang SC';
  color: #F05542;
}
.mt8{
  margin-top: 8rpx;
}
.mb8{
  margin-bottom: 8rpx;
}
.shop-icon{
  width: 24rpx;
  height: 24rpx;
  margin-right: 12rpx;
}
.hop-loction-icon{
  width: 22rpx;
  height: 26rpx;
}
.phone-icon {
  width: 56rpx;
  height: 56rpx;
  margin-right: 28rpx;
}
.flex_wrap{
  align-items: center;
}
.shop-phone-box{
  width: 56rpx;
  height: 56rpx;
  background: #F8F8F8;
  border-radius: 18rpx;
}
.shop-phone-icon{
  width: 28rpx;
  height: 28rpx;
}
.completed-icon{
  width: 40rpx;
  height: 38rpx;
  margin-right: 16rpx;
  margin-left: 20rpx;
}
.shop-title {
  width: 100%;
  color: #333;
  font: 600 32rpx/44rpx 'PingFangSC, PingFang SC';
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.shop-address{
  width: 502rpx;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; 
}