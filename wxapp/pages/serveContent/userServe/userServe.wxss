page{
  background: #F8F8F8;
}
.list .item {
  margin-bottom: 20rpx;
  padding: 20rpx;
}
.m20 {
  margin: 20rpx 20rpx 0 20rpx;
}
.list .item .name {
  color: #333;
  font: 500 32rpx/44rpx 'PingFangSC, PingFang SC';
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; 
  overflow: hidden;
  margin-bottom: 20rpx;
  width: 404rpx;
}
.money {
  align-items: flex-start;
}
.list .item .text-contet text{
  color: #666;
  font: 400 28rpx/40rpx 'PingFangSC, PingFang SC';
  margin-bottom: 8rpx;
  
}
.text-contet .text-hidden {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1; 
  overflow: hidden;
}
.text-title {
  width: 250rpx;
}
.item image {
  width: 100rpx;
  height: 100rpx;
  vertical-align: top;
  border-radius: 10rpx;
  margin-right: 16rpx;
}
.text-order {
  color: #666;
  font: 400 28rpx/40rpx 'PingFangSC, PingFang SC';
}
.text-pay {
  color: #999;
  font: bold 28rpx/40rpx 'PingFangSC, PingFang SC';
}
.payed{
  color: #367DFF;
}
.item-middle {
  margin-bottom: 20rpx;
  margin-top: 28rpx;
}