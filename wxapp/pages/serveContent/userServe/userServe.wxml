<!--pages/people/people.wxml-->
<view class="list" style="padding-bottom:160rpx">
	<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
	<view wx:if="{{list.length>0}}">
		<view class="item bg-color-white m20" wx:for="{{list}}" wx:key="index"
			data-id="{{item.id}}" data-index="{{index}}" catchtap="gotoDetail">
			<view class="flex_lr_m">
				<text class="text-order">订单号：{{item.orderSn}}</text>
				<text class="text-pay {{item.payStatus === 2 ? 'payed': ''}}">{{payStatusMap[item.payStatus]}}</text>
			</view>
			<view class="flex_lr_m item-middle">
				<view class="flex_b">
					<image src="{{item.productImgUrl}}" mode="aspectFill">></image>
					<view class="name">{{ item.productName }}</view>
				</view>
				<view class="money">¥{{ item.price}}</view>
			</view>
			<view  data-index="{{index}}">
				<view class="text-contet flex_lr_m">
					<text class="text-title">预约门店：</text> <text class="text-hidden"> {{ item.storeName }}</text>
				</view>
				<view class="text-contet flex_lr_m">
					<text >预约日期：</text> <text> {{ item.checkTime }}</text>
				</view>
				<view class="text-contet flex_lr_m">
					<text >预约人：</text> <text> {{ item.inquirerName }}</text>
				</view>
			</view>
			
		</view>
	</view> 
</view>
