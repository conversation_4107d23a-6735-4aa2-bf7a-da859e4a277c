// pages/people/people.js
var api = require('../../../config/api.js')
const util = require('../../../utils/util.js')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list: [],
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '服务商品',
    listQuery: {
      page: 1
    },
    payStatusMap: {
      1: '待支付',
      2: '已支付',
      3: '支付失败',
      4: '支付中',
      5: '关闭订单',
      6: '已退款'
    }
  },
  // 活动详情
  gotoDetail: function(e) {
    const id = e.currentTarget.dataset.id//成人还是儿童
    // const index = e.currentTarget.dataset.index
    wx.navigateTo({
      url: `/pages/serveContent/userServeDetail/userServeDetail?id=${id}`
    })
  },
 
  // 服务商品列表
   async getList(type) {
     util.showToast({
       title: '加载中..',
       icon: 'loading'
     })
     if (type !== 1 && type !== 2) {
       type = 1
     }
     var that = this
     try {
       const { data } = await util.request(
         api.hpvProduct + `/order/list`,
         that.data.listQuery,
         'get',
         '1'
       )
       if (!data) {
         return false
       }
       const result = data.data
       console.log(result, '======data======')
       if (data.code === 0) {
         that.setData({
           list:
             type === 1 ? result.result : that.data.list.concat(result.result),
           loadComplete: result.hasNext
         })
       } else {
         util.showToast({
           icon: 'none',
           title: data.msg
         })
       }
       return true
     } catch (error) {
       return false
     }
   },
   /**
    * 生命周期函数--监听页面加载
    */
   onLoad: function(options) {
     this.getList(1)
   },
   onReachBottom: function() {
     if (this.data.loadComplete) {
       this.setData({
         ['listQuery.page']: ++this.data.listQuery.page
       })
       this.getList(2)
     }
 
   },

})
