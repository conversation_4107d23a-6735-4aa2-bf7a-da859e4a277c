// pages/user/user.js
const api = require('../../config/api.js')
const util = require('../../utils/util')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    imgObject: {
      bg_doctor: api.ImgUrl + 'images/bg_doctor.png',
      ic_my_more: api.ImgUrl + 'images/ic_my_more.png',
      ic_my_service: api.ImgUrl + 'images/ic_my_service.png',
      ic_my_patient: api.ImgUrl + 'images/ic_my_patient.png',
      ic_my_prescription: api.ImgUrl + 'images/ic_my_prescription.png',
      ic_my_address: api.ImgUrl + 'images/ic_my_address.png',
      ic_more_grey: api.ImgUrl + 'images/ic_more_grey.png',
      ic_my_follow: api.ImgUrl + 'images/ic_my_follow.png',
      nomes: api.ImgUrl + 'images/nomes.png',
      ic_my_consultation: api.ImgUrl + 'images/ic_my_consultation.png',
      ic_about_us: '../../static/images/ic_my_about.png',
      ic_my_more_red: api.ImgUrl + '/images/shop/ic_my_more_red.png'
    },
    userName: null,
    userPhone: null,
    userAvatar: null,
    orderMenu: [{
      icon: api.ImgUrl + 'images/ic_my_paid.png',
      name: '待支付',
      num: 0,
      type: 1
    }, {
      icon: api.ImgUrl + 'images/ic_my_delivered.png',
      name: '待发货',
      num: 0,
      type: 2
    }, {
      icon: api.ImgUrl + 'images/ic_my_receiving.png',
      name: '待收货',
      num: 0,
      type: 3
    }, {
      icon: api.ImgUrl + 'images/ic_my_cancle.png',
      name: '已结束',
      num: 0,
      type: 5
    }],
    isBack: false,
    csPhone: '',
    version: '当前版本：' + app.globalData.version,
    realStatus: 1,
    defaultAvatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132',
    defaultInquirerId: ''
  },
  // 我的订单
  allOrder() {
    wx.navigateTo({
      url: '/pages/orderList/orderList'
    })
  },
  call() {
    wx.makePhoneCall({
      phoneNumber: this.data.csPhone
    })
  },
  // 订单数量
  async statisticalInfo() {
    var userInfo = await util.getBaseInfo()
    console.log(userInfo, '======userInfo=====')
    this.setData({
      userAvatar: userInfo.photo,
      userName: userInfo.name,
      userPhone: util.stringHidden(userInfo.phone, 3, 4),
      realStatus: userInfo.realStatus,
      defaultInquirerId: userInfo.defaultInquirerId
    })
    util.request(api.statisticalInfo)
      .then(res => {
        if (res.data.code === 0) {
          this.setData({
            ['orderMenu[' + 0 + '].num']: res.data.data.pendingNum,
            ['orderMenu[' + 1 + '].num']: res.data.data.transferSuccessNum,
            ['orderMenu[' + 2 + '].num']: res.data.data.shippingNum
          })
        }
      })
      .catch(res => {})

  },
  getPhoneNum() {
    util.request(api.getPhoneNum, {}, 'get')
      .then(res => {
        if (res.data.code === 0) {
          this.setData({
            csPhone: res.data.data.csPhone
          })
        }
      })
      .catch(res => {})
  },
  // 上传头像
  onChooseAvatar(e) {
    const {
      avatarUrl
    } = e.detail
    const sysInfo = wx.getSystemInfoSync()
    util.showLoading({
      title: '上传中~',
      mask: true
    })
    wx.uploadFile({
      url: api.uplodPhoto,
      filePath: avatarUrl,
      name: 'file',
      header: {
        'content-type': 'multipart/form-data',
        'Authorization': wx.getStorageSync('token'),
        '_m': sysInfo.model,
        '_o': 0,
        '_w': 1
      },
      success: (res) => {
        var data = JSON.parse(res.data)
        const userInfo = wx.getStorageSync('userInfo')
        userInfo.avatar = data.data
        util.setUserInfo(userInfo)
        this.setData({
          userAvatar: data.data
        })
      },
      complete: () => {
        util.hideLoading()
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    // const userInfo = wx.getStorageSync('userInfo')
    // if (userInfo) {
    //   this.getPhoneNum()
    // }
  },
  // 打开企业微信客服聊天
  handleOpenServiceChat() {
    wx.openCustomerServiceChat({
      extInfo: {
        url: 'https://work.weixin.qq.com/kfid/kfc78baf37080201066'
      },
      corpId: 'ww76dc890247c546fd',
      success(res) {
        console.log(res, 'res')
      }
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo.token) {
      this.statisticalInfo()
      this.getPhoneNum()
    }
  },
  handleContact(e) {
    console.log(e.detail.path)
    console.log(e.detail.query)
  },
  handleOpenJD() {
    wx.navigateToMiniProgram({
      appId: 'wx91d27dbf599dff74',
      path: 'pages/gold/item/pages/detail/index?sku=10054390446190',
      extraData: {
        foo: 'bar'
      },
      envVersion: 'release',
      success(res) {
        // 打开成功
      }
    })
  },
  async goRealAuthent() {
    if (this.data.defaultInquirerId) {
      wx.navigateTo({
        url: '/pages/peopleContent/detail/detail?model=0&inquirerId=' + this.data.defaultInquirerId
      })
    } else {
      wx.navigateTo({
        url: '/pages/peopleContent/addPeople/addPeople'
      })
    }
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  }

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage: function() {

  // }
})