/* pages/user/user.wxss */
page{
  background: #F8F8F8;
}
.bgImg{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 500rpx;
}
.userInfo{
  padding-left: 60rpx;
  padding-top: 30rpx;
}
.userInfo .photo{
  float: left;
  width: 136rpx;
  height: 136rpx;
  border-radius: 50%;
  background: #fff;
  overflow: hidden;
  padding: 4rpx;
}
.userInfo .avatar {
  display: block;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 50%;
}
.orderMenu{
  margin-top: 86rpx;
}
.orderMenu2 {
  margin-top: 10rpx;
}
.br20{
  border-radius: 20rpx;
}
.more{
  color: #8B8B8B;
}
.more image{
  display: inline-block;
  width: 20rpx;
  height: 44rpx;
  vertical-align: top;
}
.menu_warp .item{
  width: 25%;
  float: left;
}
.menu_warp .item .icon{
  width: 100%;
  text-align: center;
  width: 50rpx;
  height: 50rpx;
  margin: 0 auto;
  margin-top: 8rpx;
  position: relative;
}
.menu_warp .item .icon text{
  position: absolute;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: var(--redColor);
  line-height: 32rpx;
  color: #fff;
  font-size: 22rpx;
  font-weight: bold;
  top: -12rpx;
  left: 32rpx;
}
.menu_warp .item .icon text.cur{
  width: auto;
  padding: 0 8rpx;
  border-radius: 19rpx;
}
.menu_warp .item .icon image{
  display: block;
  width: 50rpx;
  height: 50rpx;
}
.menu_warp .item view.name{
  padding-top: 8rpx;
}
.menu .item .icon{
  float: left;
}
.menu .item  text{
  float: left;
  display: block;
  height: 44rpx;
  margin-left: 16rpx;
}
.menu .item image{
  display: block;
  float: right;
  width: 44rpx;
  height: 44rpx;
}
.real_authent {
  max-width: 300rpx;
  background: rgba(255,255,255,0.01);
  border-radius: 8rpx;
  border: 1rpx solid #DF4634;
  display: flex;
  align-items: center;
  padding: 12rpx;
  margin-top: 8rpx;
}
.real_text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #DF4634;
  margin-right: 8rpx;
}
.icon_more {
  width: 40rpx;
  height: 44rpx;
}
.mod {
  width: 368rpx;
  height: 48rpx;
  background: #DDEFFF;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  position: relative;
  /* top: 40rpx; */
  top: -15rpx;
}
.tips {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #367DFF;
}
.mod:before {
  content: "";
  width: 0px;
  height: 0px;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 10px solid #DDEFFF;
  position: absolute;
  top: -10px;
  left: 28px;
}