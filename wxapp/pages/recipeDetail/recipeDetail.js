// pages/recipeDetail/recipeDetail.js
const api = require('../../config/api')
const util = require('../../utils/util')
const app = getApp()

Page({
  /**
	 * 页面的初始数据
	 */
  data: {

    type: null,
    recommendId: null,
    detail: {},
    imgObject: {
      reviewedNo: api.ImgUrl + 'images/ic_prescription_reviewed_no.png',
      reviewedOk: api.ImgUrl + 'images/ic_prescription_reviewed_ok.png',
      reviewedNot: api.ImgUrl + 'images/ic_prescription_reviewed_wait.png',
      ic_prescription_seal: api.ImgUrl + 'logo/ic_seal.png'
    },
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '处方详情',
    templateId: [],
    authShow: false,
    hainanConfig: null
  },
  onClose() {
    this.authToast.setData({
      authShow: false
    })
    wx.navigateTo({
      // url: '/pages/confirmOrder/confirmOrder?id=' + this.data.detail.recommendId
      url: `/pages/confirmOrder/confirmOrder?id=${that.data.detail.recommendId}&source=1`
    })
  },
  async getDetail() {
    try {
      const {
        data
      } = await util.request(api.prescriptionDetail, {
        recommendId: this.data.recommendId
      }, 'post', 2)
      if (data.code !== 0) {
        throw new Error(data.msg)
      }
      data.data.recomTime = util.parseTime(data.data.recomTime, '{y}.{m}.{d}')
      data.data.doctorAuditTime = util.parseTime(data.data.doctorAuditTime, '{y}-{m}-{d} {h}:{i}:{s}')
      data.data.pharmacistAuditTime = util.parseTime(data.data.pharmacistAuditTime, '{y}-{m}-{d} {h}:{i}:{s}')
      data.data.alPharmacistAuditTime = util.parseTime(data.data.alPharmacistAuditTime, '{y}-{m}-{d} {h}:{i}:{s}')

      this.setData({
        detail: data.data
      })
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
  },
  onAuthSub(type) {
    // type=1 首次获取 否则第二次弹窗
    var that = this
    if (this.data.templateId.includes(null)) {
      // source 1.处方 2.商品 3.购物车
      wx.navigateTo({
        // url: '/pages/confirmOrder/confirmOrder?id=' + that.data.detail.recommendId
        url: `/pages/confirmOrder/confirmOrder?id=${that.data.detail.recommendId}&source=1`
      })
      return false
    }
    wx.requestSubscribeMessage({
      tmplIds: that.data.templateId,
      success: (res) => {
        if (res[that.data.templateId[0]] === 'reject' && type === 1) {
          this.authToast.setData({
            authShow: true
          })
        } else {
          wx.navigateTo({
            // url: '/pages/confirmOrder/confirmOrder?id=' + that.data.detail.recommendId
            url: `/pages/confirmOrder/confirmOrder?id=${that.data.detail.recommendId}&source=1`
          })
        }
      },
      fail: (res) => {
        if (type === 1) {
          this.authToast.setData({
            authShow: true
          })
        } else {
          wx.navigateTo({
            // url: '/pages/confirmOrder/confirmOrder?id=' + that.data.detail.recommendId
            url: `/pages/confirmOrder/confirmOrder?id=${that.data.detail.recommendId}&source=1`
          })
        }

      }
    })
  },
  buyDrug(e) {
    if (!(this.data.type == 1 || !(this.data.detail.buy == 1 || this.data.detail.expire == 1 || this.data.detail.invalid == 1))) {
      return false
    } else {
      this.onAuthSub(1)
    }
  },
  applyFor(e) {
    this.reapply(e.target.dataset.id)
  },
  async reapply(recommendId) {
    try {
      util.showLoading({
        title: '提交中',
        mask: true
      })
      const {
        data
      } = await util.request(api.prescriptionReapply, {
        recommendId
      }, 'post', 2)
      // TODO: 暂定 申请再次购买后改变状态
      this.setData({
        'detail.require': 1
      })
      util.hideLoading()
      if (data.code === 0) {
        util.showModal({
          content: '您的求药申请已发送给' + this.data.detail.doctorName + '医生，医生会第一时间为您续方，请耐心等待~',
          showCancel: false,
          confirmText: '我知道了',
          success: (result) => {}
        })
      } else if (data.code === 210) {
        util.showModal({
          content: data.msg,
          showCancel: false,
          confirmText: '确定',
          success: function(res) {}
        })
      } else {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
      }
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
  },
  // 获取用药模板
  async getTemplate() {
    var templateId = await util.getTemplate(4)
    this.setData({
      templateId: templateId
    })
  },
  /**
	 * 生命周期函数--监听页面加载
	 */

  async onLoad(options) {
    const hainanConfig = await util.getHaiNanConfig()
    if (!options.recomId) {
      return wx.navigateBack({
        delta: 1
      })
    }
    this.setData({
      type: options.type,
      recommendId: options.recomId,
      hainanConfig
    })
    this.getDetail()
    this.getTemplate()
  },
  download() {
    app.globalData.pdfUrl = this.data.detail.pdfUrl
    wx.navigateTo({
      url: '/pages/pdf/index'
    })
  },
  /**
	 * 生命周期函数--监听页面显示
	 */
  onShow: function() {
    var that = this
    that.authToast = this.selectComponent('#authToast')
    // setTimeout(() => {
    //   that.getDetail()
    //   that.getTemplate()
    // }, 500)
  },
  touchMove() {
    util.verifyPageMessageUpdate()
  },
  /**
	 * 生命周期函数--监听页面隐藏
	 */
  onHide: function() {
    this.setData({
      authShow: false
    })
  },

  /**
	 * 生命周期函数--监听页面卸载
	 */
  onUnload: function() {
  },

  /**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
  onPullDownRefresh: function() {

  },

  /**
	 * 页面上拉触底事件的处理函数
	 */
  onReachBottom: function() {

  }

  /**
	 * 用户点击右上角分享
	 */
  // onShareAppMessage: function() {

  // }
})
