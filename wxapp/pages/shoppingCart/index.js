const util = require('../../utils/util')
const api = require('../../config/api')
Page({
  data: {
    list: [],
    totalPrice: 0,
    selectedCount: 0,
    checkedAll: false,
    noData: false
  },
  getList() {
    util.request(api.cartsInfo).then(res => {
      this.setCartData(res)
    })
  },
  merchandiseChange(e) {
    util.request(api.updateQuantity + `?quantity=${e.detail.number}&itemId=${e.detail.goods.itemId}`, {}, 'post').then(res => {
      this.setCartData(res)
    })
  },
  deleteGoods(e) {
    util.request(api.cartsDelete + `?itemId=${e.currentTarget.dataset.item.itemId}&recomId=0`, {}, 'post').then(res => {
      this.setCartData(res)
    })
  },
  setCartData(res) {
    const list = res.data.data.groups.length > 0 ? res.data.data.groups[0].items : []
    list.forEach(item => {
      item.checked = item.selected === 1
    })
    this.setData({
      checkedAll: list.every(item => item.checked),
      list,
      totalPrice: res.data.data.totalPrice,
      selectedCount: res.data.data.selectedCount || 0,
      noData: list.length === 0
    })
  },
  checkedChange(e) {
    const data = e.currentTarget.dataset.item
    util.request(api.updateSelected + `?itemId=${data.itemId}&all=0&selected=${e.detail ? 1 : 0}&recomId=0`, {}, 'post').then(res => {
      this.setCartData(res)
    })
  },
  onAllChange(e) {
    util.request(api.updateSelected + `?all=1&selected=${e.detail ? 1 : 0}&itemId=0&recomId=0`, {}, 'post').then(res => {
      this.setCartData(res)
    })
  },
  submit() {
    wx.navigateTo({
      // url: '/pages/shoppingCartSubmit/index'
      url: `/pages/confirmOrder/confirmOrder?source=3`
    })
  },
  onLoad: function(options) {
  },
  onShow: function() {
    this.getList()
  }
})