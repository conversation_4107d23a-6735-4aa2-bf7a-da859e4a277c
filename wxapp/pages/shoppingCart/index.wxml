<navbar isBack="{{true}}" backgroundColor="#fff" navTitle="购物车"></navbar>
<view class="outer">
  <view class="cart-wrap">
    <van-swipe-cell wx:for="{{list}}" wx:key="itemId" right-width="{{ 64 }}">
      <van-checkbox disabled="{{item.status === 0 || item.stockStatus === 0}}" value="{{ item.checked }}" data-item="{{item}}" bind:change="checkedChange">
        <view class="goods-wrap" catch:tap>
          <merchandise horizontal showStepper showCar="{{false}}" detail="{{item}}" bind:change="merchandiseChange"></merchandise>
        </view>
      </van-checkbox>
      <view slot="right" class="van-swipe-cell__right" data-item="{{item}}" bind:tap="deleteGoods">删除</view>
    </van-swipe-cell>
  </view>
</view>
<view class="bottom-bar">
  <view class="btns-wrap">
    <van-checkbox value="{{ checkedAll }}" bind:change="onAllChange">
      全选
    </van-checkbox>
    <view class="bottom-bar-right">
      <view class="price-wrap">
        合计：<text class="red">¥</text><text class="price red">{{totalPrice}}</text>
      </view>
      <button disabled="{{selectedCount === 0}}" class="i-btn" bind:tap="submit">结算<text>({{selectedCount}})</text></button>
    </view>
  </view>
</view>

<no-data wx:if="{{noData}}" text="空空如也～"></no-data>