page {
  background: #f8f8f8;
}
.outer {
  padding-bottom: calc(env(safe-area-inset-bottom) + 96rpx);
}
.cart-wrap {
  margin: 20rpx;
  background: #fff;
  padding-left: 20rpx;
  border-radius: 8rpx;
}
.bottom-bar {
  background: #fff;
  position: fixed;
  bottom: 0;
  width: 100%;
  padding-bottom: env(safe-area-inset-bottom);
}
.bottom-bar .btns-wrap {
  padding: 20rpx 28rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.bottom-bar-right {
  display: flex;
  align-items: center;
}
.bottom-bar-right .price-wrap {
  margin-right: 50rpx;
  font-size: 24rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #666666;
}
.bottom-bar-right .price-wrap .price {
  font-size: 32rpx;
}
.red {
  color: #f05542 !important;
}
.i-btn {
  background: #367dff;
  width: 160rpx;
  height: 56rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 26rpx;
}
.van-swipe-cell__right {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background: #f05542;
  width: 128rpx;
  height: 100%;
}
.goods-wrap {
  width: 640rpx;
}
