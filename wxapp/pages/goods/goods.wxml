<!-- pages/search/search.wxml -->
<view wx:if="{{goodsDetail.status === 1}}" class="container">
  <navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
  <view class="goods-wrap">
    <view class="page-section page-section-spacing swiper">
      <view wx:if="{{goodsDetail.rx === 1 }}" class="cover layer">
        <view>处方药</view>
        <view>凭处方在医师</view>
        <view>指导下购买使用</view>
      </view>
      <swiper indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}" circular="{{circular}}" vertical="{{vertical}}" interval="{{interval}}" duration="{{duration}}" previous-margin="{{previousMargin}}px" next-margin="{{nextMargin}}px" current="{{currentNum}}" bindchange='onSwiperChange'>
        <block wx:for="{{goodsDetail.imgs}}" wx:key="*this">
          <swiper-item>
            <view class="swiper-item-inner">
              <image src="{{item.large}}" class="imgBlock"></image>
            </view>
          </swiper-item>
        </block>
      </swiper>
      <view class="swiper-item-num">
        <view>{{currentNum ? currentNum + 1 : 1}}</view>
        <view>/</view>
        <view>{{goodsDetail.imgs.length}}</view>
      </view>
    </view>
    <view class="goods-name-wrap">
      <view class="goods-name">
        <view class="name">{{goodsDetail.name}}</view>
      </view>
      <view class="price-warp">
        <view class="unit">¥</view>
        <view class="price">{{goodsDetail.displayPrice}}</view>
      </view>
    </view>
    <view class="goods-detail">
      <view class="goods-detail-title">商品信息</view>
      <view class="goods-detail-content">
        <view class="content-item" wx:for="{{goodsDetail.lower}}" wx:key="index">
          <view class="label">{{item.attributeKey}}</view>
          <view class="content">{{item.attributeValue || '--'}}</view>
        </view>
      </view>
    </view>

    <view wx:if="{{goodsDetail.type == 1}}" class="footer">
      <view wx:if="{{goodsDetail.rx === 2 && !applicationConfig.config.otcConsultBuying}}" class="footer">
        <view class="footer-content">
          <view class="left" bind:tap="goCart">
            <van-icon name="cart-o" color="#333" />
            <view>购物车</view>
          </view>
          <button open-type="share" class="custom-share-button">
            <image src="{{shareIcon}}" mode="widthFix" class="share-icon"/>
            <view class="share-text">分享</view>
          </button>
          <view class="right" wx:if="{{goodsDetail.quantity > 0}}">
            <view class="btn cart-btn" bindtap='addCart'>加入购物车</view>
            <view class="btn buy-btn" bindtap='goBuy'>立即购买</view>
          </view>
          <!-- 无货显示的按钮 -->
          <view wx:if="{{goodsDetail.quantity === 0}}" class="right">
            <view class="btn no-btn">当前无货</view>
          </view>
        </view>
      </view>

      <view class="footer" wx:elif="{{goodsDetail.rx == 2 && applicationConfig.config.otcConsultBuying}}">
        <view class="footer-content" style="justify-content: flex-end;">
          <view class="right">
            <view class="btn buy-btn" bindtap='goConsult'>去问诊</view>
          </view>
        </view>
      </view>

      <view wx:else>
        <view class="share-btn">
          <button open-type="share" class="custom-share-button">
            <image src="{{shareIcon}}" mode="widthFix" class="share-icon"/>
            <view class="share-text">分享</view>
          </button>
        </view>
      </view>
    </view>

    <view wx:else>
      <view class="footer">
        <view class="footer-content">
          <view class="left" bind:tap="goCart">
            <van-icon name="cart-o" color="#333"/>
            <view>购物车</view>
          </view>
          <button open-type="share" class="custom-share-button">
            <image src="{{shareIcon}}" mode="widthFix" class="share-icon"/>
            <view class="share-text">分享</view>
          </button>
          <view class="right" wx:if="{{goodsDetail.quantity > 0}}">
            <view class="btn cart-btn" bindtap='addCart'>加入购物车</view>
            <view class="btn buy-btn" bindtap='goBuy'>立即购买</view>
          </view>
          <!-- 无货显示的按钮 -->
          <view wx:if="{{goodsDetail.quantity === 0}}" class="right">
            <view class="btn no-btn">当前无货</view>
          </view>
        </view>
      </view>
    </view>
      
  </view>
  <!-- 弹框 -->
  <van-popup show="{{ show }}" position="bottom" closeable close-icon="close" safe-area-inset-bottom bind:close="onClose">
    <view class="choose-sku">
      <view class="top">
        <image src="{{goodsDetail.imgs[0].large}}" mode='widthFix' class="imgBlock"></image>
        <view class="detail">
          <view class="detail-inner">
            <view class="name">{{goodsDetail.name}}</view>
          </view>
          <view class="price">¥ {{goodsDetail.displayPrice ? goodsDetail.displayPrice : goodsDetail.salePrice}}</view>
        </view>
      </view>
      <view class="center">
        <view class="section-title">规格</view>
        <view class="sku-warp">
          <view wx:for="{{goodsDetail.skus}}" wx:key="index" bindtap='chooseSku' data-id="{{item.skuId}}" class="sku-item {{currentSku.skuId == item.skuId && currentSku.quantity !== 0 ? 'sku-item-active':''}}">
            {{item.packingSpec || '--'}}
          </view>
        </view>
      </view>
      <view class="bottom">
        <view class="section-title">数量</view>
        <view>
          <van-stepper value="{{ 1 }}" disabled="{{!currentSku.skuId}}" max="{{currentSku.quantity}}" input-width="40px" button-size="32px" bind:change="steprChange" />
        </view>
      </view>
      <view class="btn-warp">
        <view hidden="{{currentSku.quantity === 0}}" class="btn {{!currentSku.skuId ? 'disabled' : ''}}" bind:tap="submit">
          {{btnText}}
        </view>
        <view wx:if="{{currentSku.quantity === 0}}" class="btn disabled">当前无货</view>
      </view>
    </view>
  </van-popup>
</view>
<nodata wx:if="{{goodsDetail.status === 0}}" text="该商品已下架"></nodata>
<van-toast id="van-toast" />
