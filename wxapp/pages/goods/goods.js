// pages/people/people.js
var api = require('../../config/api.js')
const util = require('../../utils/util')
import Toast from '../../lib/vant-weapp/toast/toast'
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '商品详情',
    activeNames: ['1'],
    background: ['demo-text-1', 'demo-text-2', 'demo-text-3'],
    indicatorDots: false, // 是否显示面板指示点
    vertical: false,
    autoplay: true,
    circular: true,
    interval: 2000,
    duration: 500,
    previousMargin: 0,
    nextMargin: 0,
    show: false,
    btnText: '立即购买',
    currentSku: {},
    skuNumber: 1,
    goodsDetail: {},
    saveCurrentSku: {},
    applicationConfig: {},
    shareIcon: api.ImgUrl + 'images/shop/ic_share.png'
  },
  // 切换Collapse折叠面板
  onChange(event) {
    console.log(event.detail)
    this.setData({
      activeNames: event.detail
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.init(options)
  },

  async init(e) {
    let objStr = JSON.stringify(e)
    const userInfo = wx.getStorageSync('userInfo')
    let response = null
    if (!userInfo.userId) {
      response = await util.loginByWeixin()
    }
    if (response && response.data.code === 0 && response.data.data.loginStatus === 1) {
      // 切换到登录页面
      wx.navigateTo({
        url: '/pages/auth/login/login?params=' + encodeURIComponent(objStr)
      })
    } else {
       this.setData({
        userId: app.globalData.userInfo.userId,
       }, () => {
        this.getDetail(e.skuId)
        if (!e.shareUserId) return
        this.patientRelationBind(e.shareUserId)
       })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    const applicationConfig = wx.getStorageSync('applicationConfig')
    this.setData({
      applicationConfig: applicationConfig
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

    /**
   * 用户点击右上角分享
   */
  onShareAppMessage(res) {
    const { userId } = app.globalData.userInfo
    const { name, skuId } = this.data.goodsDetail
    return {
      title: name,
      path: `/pages/goods/goods?skuId=${skuId}&shareUserId=${userId}`,
      withShareTicket: true
    }
  },

   // 患者关系绑定
   patientRelationBind(shareUserId) {
    try {
      const { userId } = app.globalData.userInfo
      const { relationBinds } = wx.getStorageSync('applicationConfig')
      const haspatientBind = relationBinds && relationBinds.some(bind => bind.channelType === 3)
      if (Number(shareUserId) !== Number(userId) && !haspatientBind) {
        const params = { channelUserId: shareUserId, channelType: 3 }
        util.request(api.patientRelationBind, params, 'post', 4).then(response => {
          if (response.data.code !== 0) {
            Toast({
              message: '患者关系绑定失败',
              position: 'bottom'
            })
          }
        })
      }
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // 监听轮播
  onSwiperChange(e) {
    let { current, source } = e.detail
    if (source === 'autoplay' || source === 'touch') {
      this.setData({
        currentNum: current
      })
    }
    // const {
    //   current
    // } = e.detail
    // this.setData({
    //   currentNum: current
    // })
  },
  // 判断对象是否为空
  isEmptyObject(obj) {
    for (var key in obj) {
      if (obj.hasOwnProperty(key)) {
        return false;
      }
    }
    return true;
  },
  addCart() {
    if(this.isEmptyObject(this.data.saveCurrentSku)) {
      this.setData({
        show: true,
        btnText: '加入购物车',
        currentSku: this.data.goodsDetail.skus.find(item => item.skuId === this.data.goodsDetail.skuId)
      })
    } else {
      this.setData({
        show: true,
        btnText: '加入购物车',
        currentSku: this.data.saveCurrentSku
      })
    }
  },
  goBuy() {
    if(this.isEmptyObject(this.data.saveCurrentSku)) {
      this.setData({
        show: true,
        btnText: '立即购买',
        currentSku: this.data.goodsDetail.skus.find(item => item.skuId === this.data.goodsDetail.skuId)
      })
    } else {
      this.setData({
        show: true,
        btnText: '立即购买',
        currentSku: this.data.saveCurrentSku
      })
    }
  },
  onClose() {
    this.setData({
      currentSku: {},
      skuNumber: 1,
      show: false
    })
  },
  chooseSku(e) {
    console.log(e.target.dataset.id, 'current')
    const currentSku = this.data.goodsDetail.skus.find(item => item.skuId === e.target.dataset.id)
    if(currentSku && currentSku.quantity === 0) {
      Toast({ position: 'bottom',message: '该规格已售罄~' })
      return
    }
    this.setData({
      currentSku: currentSku
    })
    this.data.saveCurrentSku = JSON.parse(JSON.stringify(this.data.currentSku))
    this.getDetail(e.target.dataset.id)
  },
  getDetail(skuId) {
    util.request(api.productDetail + '?skuId=' + skuId, {}, 'get', 1, false).then(res => {
      this.setData({
        goodsDetail: res.data.data
      })
    })
  },
  steprChange(e) {
    this.setData({
      skuNumber: e.detail
    })
  },
  goCart() {
    wx.navigateTo({
      url: '/pages/shoppingCart/index'
    })
  },
  submit() {
    if (!this.data.currentSku.skuId) {
      return
    }
    if (this.data.btnText === '加入购物车') {
      this.addCartSubmit()
    } else {
      this.setData({
        show: false
      })
      wx.navigateTo({
        // url: '/pages/shoppingCartSubmit/index?skuId=' + this.data.currentSku.skuId + '&quantity=' + this.data.skuNumber
        url: `/pages/confirmOrder/confirmOrder?skuId=${this.data.currentSku.skuId}&quantity=${this.data.skuNumber}&source=2`
      })
    }
  },
  addCartSubmit() {
    if (this.loading) {
      return
    }
    this.loading = true
    const url = api.addCarts + `?skuId=${this.data.currentSku.skuId}&quantity=${this.data.skuNumber}`
    util.request(url, {}, 'post').then(res => {
      this.loading = false
      if (res.data.code === 0) {
        Toast({
          position: 'bottom',
          message: '加购成功'
        })
        this.setData({
          currentSku: {},
          skuNumber: 1,
          show: false
        })
      }
    })
  },
  goConsult() {
      wx.navigateTo({
          url: '/pages/famousDoctor/famousDoctor'
      })
  }
})
