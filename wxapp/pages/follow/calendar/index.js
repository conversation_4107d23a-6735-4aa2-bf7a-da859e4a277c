//index.js
var api = require('../../../config/api.js')
var util = require('../../../utils/util')
const app = getApp()
Page({

  /**
	 * 页面的初始数据
	 */
  data: {
    static: {
      ic_follow_visit: api.ImgUrl + 'images/ic_follow_visit.png',
      ic_follow_questionnaire: api.ImgUrl + 'images/ic_follow_questionnaire.png',
      img_blank_noprescription: api.ImgUrl + 'images/img_blank_noprescription.png'
    },
    list: [],
    finished: false,
    listQuery: {
      page: 1,
      date: util.formatTimeTwo(new Date(), 'Y-M-D')
    },
    calendarConfig: {
      theme: 'elegant',
      showLunar: false,
      defaultDay: true,
      highlightToday: true,
      preventSwipe: true,
      emphasisWeek: true,
      hideHeader: false
    },
    statusBarHeight: 0,
    month: util.formatTimeTwo(new Date(), 'Y-M')
  },
  /**
	 * 生命周期函数--监听页面加载
	 */
  onLoad(options) {
    this.calendar = this.selectComponent('#calendar')
    this.setData({
      statusBarHeight: app.globalData.statusBarHeight,
      'listQuery.patientId': app.globalData.userInfo.userId
    }, async() => {
      app.globalData.setSelectedDays = null
      const token = wx.getStorageSync('token')
      if (!token) return
      await this.getMonthList()
      await this.getDayList()
      // 获取待办事项之后初始化日历
      this.calendar.initComp()
    })
  },
  async getMonthList() {
    try {
      const params = {
        month: this.data.month,
        patientId: this.data.listQuery.patientId
      }
      const {
        data
      } = await util.request(api.getMonthFollowup, params, 'get', 1, false)
      console.log(data, '获取当月计划')
      if (data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
      }
      app.globalData.setSelectedDays = data.data
    } catch (error) {
      throw new Error(error)
    }
  },
  async getDayList() {
    try {
      const {
        data
      } = await util.request(api.getTodayFollowup, this.data.listQuery, 'get', 1, false)
      if (data.code !== 0) {
        console.log(data, '获取当日待办')
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
      }
      wx.pageScrollTo({
        scrollTop: 0
      })
      this.setData({
        list: this.data.list.concat(data.data.result),
        finished: !data.data.hasNext
      })
    } catch (error) {
      throw new Error(error)
    }
  },
  /**
	 * 月切换事件
	 */
  onTapMonth(event) {
    const {
      newMonth,
      newYear
    } = event.detail
    this.setData({
      list: [],
      finished: false,
      'listQuery.page': 1
    }, async() => {
      this.data.month = [newYear, newMonth].map(util.formatNumber).join('-')
      await this.getMonthList()
      // 获取待办成功render重新绘制日历
      this.calendar.jump(newYear, newMonth)
    })

  },
  /**
	 * 日期点击事件
	 */
  onTapDay(event) {
    const {
      year,
      month,
      day
    } = event.detail
    this.setData({
      list: [],
      finished: false,
      'listQuery.page': 1
    }, () => {
      this.data.listQuery.date = [year, month, day].map(util.formatNumber).join('-')
      this.getDayList()
    })
  },
  goDetail(e) {
    const {
      id
    } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/follow/detail/index?id=${id}`
    })
  },
  /**
	 * 页面上拉触底事件的处理函数
	 */
  onReachBottom: function() {
    if (!this.data.finished) {
      ++this.data.listQuery.page
      this.getDayList()
    }
  },
  /**
	 * 生命周期函数--监听页面初次渲染完成
	 */
  onReady: function() {

  },

  /**
	 * 生命周期函数--监听页面显示
	 */
  onShow() {
    this.setData({
      list: [],
      finished: false,
      'listQuery.page': 1
    }, async() => {
      if (app.globalData.setSelectedDays) {
        const token = wx.getStorageSync('token')
        if (!token) return
        await this.getMonthList()
        await this.getDayList()
      }
    })
  },

  /**
	 * 生命周期函数--监听页面隐藏
	 */
  onHide: function() {

  },

  /**
	 * 生命周期函数--监听页面卸载
	 */
  onUnload: function() {

  },

  /**
	 * 用户点击右上角分享
	 */
  onShareAppMessage: function() {

  }
})
