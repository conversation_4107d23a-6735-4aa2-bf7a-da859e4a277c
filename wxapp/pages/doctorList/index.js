var api = require('../../config/api.js')
var util = require('../../utils/util')
const app = getApp()
Page({

  /**
	 * 页面的初始数据
	 */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '预约挂号',
    list: [],
    listQuery: {
      page: 1
      // labelId: 2
    },
    tapTime: ''
  },

  /**
	 * 生命周期函数--监听页面加载
	 */
  onLoad: function(options) {
    this.setData({
      navTitle:options.text,
      ['listQuery.departmentId']: options.departmentId
    })
  },
  onShow(){
    this.getList()
  },
  async getList(type) {
    util.showLoading({
      title: 'loading',
      mask: true
    })
    try {
      const {
        listQuery,
        list
      } = this.data
      const {
        data
      } = await util.request(api.clinicList, {
        cityId: app.globalData.cityId,
        ...listQuery
      }, 'post', 2)
      if (data.code !== 0) {
        throw new Error(data.msg)
      }
      this.setData({
        list: listQuery.page > 1 ? list.concat(data.data.result) : data.data.result,
        loadComplete: !data.data.hasNext ? false : true
      })
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
    util.hideLoading()
  },
  /**
	 * 生命周期函数--监听页面隐藏
	 */
  onHide: function() {

  },

  /**
	 * 生命周期函数--监听页面卸载
	 */
  onUnload: function() {

  },
  onReachBottom: function() {
    if (this.data.loadComplete) {
      this.setData({
        ['listQuery.page']: ++this.data.listQuery.page
      })
      this.getList()
    }
  },
  /**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
  onPullDownRefresh: function() {

  },

  /**
	 * 用户点击右上角分享
	 */
  onShareAppMessage: function() {

  }
})
