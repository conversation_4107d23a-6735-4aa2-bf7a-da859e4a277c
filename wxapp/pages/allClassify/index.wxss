page {
  background: #f5f7fa;
}

.page-wrap {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: url('https://patient-shopdev.naiterui.com/images/shop/bg_drug_classification.png') no-repeat;
  background-size: contain;
  overflow: hidden;
}

.flex {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.good-search {
  position: sticky;
  width: 100%;
  z-index: 99;
}

.search-icon {
  width: 44rpx;
  height: 44rpx;
  margin-right: 10rpx;
  flex-shrink: 0;
}

.cart {
  width: 44rpx;
  height: 44rpx;
  position: relative;
  left: 15rpx;
}

.cart .cart-icon {
  width: 100%;
  height: 100%;
}

.number {
  position: absolute;
  width: 30rpx;
  height: 30rpx;
  border-radius: 100%;
  top: -20rpx;
  right: -20rpx;
  background: red;
  color: #fff;
  font-size: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.classify-search {
  padding: 8rpx 0rpx 10rpx 28rpx !important;
  /* height: 72rpx !important; */
}

.scroll_classify {
  white-space: nowrap;
  overflow-x: auto;
  display: flex;
  align-items: center;
}

.goods_classify {
  display: flex;
  padding-left: 28rpx;
}

.classify_item {
  margin-right: 32rpx;
}

.image_wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 92rpx;
  height: 92rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(230, 245, 255, 0) 0%, #d3eaf7 100%);
}

.image_wrap image {
  display: block;
  padding: 0 6rpx;
  width: 100%;
  height: 100% !important;
}

.classify_name {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #525252;
  white-space: nowrap;
  margin-top: 10rpx;
}

.classify-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.icon {
  width: 96rpx;
  height: 96rpx;
}

.text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #525252;
  margin-top: 8rpx;
  padding: 2rpx 12rpx;
}

.classify-item-active .icon {
  border: 2rpx solid #367dff;
  border-radius: 50%;
}

.classify-item-active .text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
  background: #367dff;
  border-radius: 18rpx;
  padding: 2rpx 12rpx;
}

.goods {
  display: flex;
}

.more-btn {
  width: 64rpx;
  height: 176rpx;
  background: #f4f9fc;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.more-btn text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 24rpx;
  color: #525252;
  line-height: 34rpx;
  margin-bottom: 8rpx;
}

.more-btn image {
  width: 24rpx;
  height: 24rpx;
  display: block;
}

/* 左侧选项样式类 */
.main-item-class {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 28rpx !important;
  color: #525252 !important;
  padding: 30rpx 12rpx 30rpx 16rpx !important;
  /* margin-bottom: 10rpx; */
}

/* 左侧选项选中样式类 */
.main-active-class {
  font-weight: 500 !important;
  font-size: 28rpx !important;
  color: #367dff !important;
  /* border-color: #367dff!important; */
  border-left: none !important;
  margin-left: 3px !important;
  background: #ffffff;
  border-radius: 20rpx 0rpx 0rpx 20rpx;
}

.main-active-class::before {
  content: '';
  position: absolute;
  left: 0;
  height: 20px;
  width: 3px;
  border-radius: 5px;
  background-color: #367dff;
}

/* 左侧区域样式 */
.van-tree-select__nav {
  background-color: #f5f7fa !important;
  flex: none !important;
  width: 200rpx;
}

.van-search__content--round {
  border: 1rpx solid #dfdfdf;
  height: 72rpx;
  line-height: 72rpx;
}

.van-tree-select {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.all-classify {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  /* background-color: ; */
  /* background: linear-gradient(180deg, #f4f9fc 0%, #fafafa 100%); */
  /* padding-top: 137px; */
  background: url('https://patient-shopdev.naiterui.com/images/shop/bg_drug_classification_popup.png') no-repeat !important;
  background-size: cover !important;
}

.all_text {
  color: #333;
  font-size: 30rpx;
  margin: 100px 42rpx 20rpx;
}

/* .scroll_popup_view {
    margin-top: 111px;
    background: linear-gradient(180deg, #f4f9fc 0%, #fafafa 100%);
  } */

.popup_classify {
  display: flex;
  flex-wrap: wrap;
  margin: 0 42rpx;
}

.popup-item {
  width: calc((100% - 184rpx) / 5);
  margin-right: 46rpx !important;
  margin-bottom: 32rpx !important;
}

.popup-item:nth-child(5n) {
  margin-right: 0 !important;
}

.retract {
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: 1px solid #eee;
  padding: 22rpx 0;
}

.retract view {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #525252;
}

.retract image {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;
}

.good-wrap {
  margin: 0rpx 20rpx;
}

.condition-bar {
  /* border-bottom: 0.5rpx solid #eee; */
  height: 76rpx;
  display: flex;
  align-items: center;
  font-size: 26rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  position: fixed;
  width: 100%;
  background-color: #fff;
  z-index: 100;
}

.condition-bar .active {
  color: #367dff;
}

.condition-bar .condition-item {
  margin-right: 60rpx;
  display: flex;
  align-items: center;
}

.condition-bar .condition-item .orderby-img {
  width: 36rpx;
  height: 36rpx;
}

.merchandise-wrap {
  padding-top: 96rpx;
}

.load-more {
  text-align: center;
  margin: 20rpx auto;
  font-size: 26rpx;
  color: #333;
}
