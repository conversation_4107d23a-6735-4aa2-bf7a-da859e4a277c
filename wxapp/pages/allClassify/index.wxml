<view class="page-wrap">
  <navbar isBack="{{true}}" navTitle="{{navTitle}}"></navbar>
  <!-- 搜索区域 -->
  <view class="good-search" id="search" bindtap="toSearchPage">
    <van-search
        value="{{value}}"
        shape="round"
        readonly
        placeholder="搜索药品通用名/商品名称"
        use-action-slot
        use-left-icon-slot
        background="transparent"
        custom-class="classify-search"
        customStyle="padding: 7.2px 10px 7.5px 0; background-color: transparent;"
        >
        <image class="search-icon" slot="left-icon" src="{{ic_input_search}}" />
        <view class="flex" slot="action">
            <view class="cart" catch:tap="onCart">
                <image class="cart-icon" style="width: 44rpx;height: 44rpx;" src="{{ic_cart}}" />
                <view wx:if="{{cartNumber > 0}}" class="number">{{cartNumber}}</view>
            </view>
        </view>
    </van-search>
  </view>
  <!-- 一级分类 -->
  <view class="goods">
    <scroll-view
      id="scroll-view"
      class="scroll_classify"
      scroll-x="true"
      scroll-left="{{scrollLeft}}"
      scroll-with-animation="{{true}}"
      >
      <view class="goods_classify">
        <view class="classify-item {{classifyActive === item.id ? 'classify-item-active' : ''}}" id="item-{{item.id}}"
          wx:for="{{allList}}" wx:key="index" data-item="{{item}}" bindtap="onClickClassify">
            <image class="icon" src="{{item.icon || img_default_classification}}" />
            <view class="text">{{item.name}}</view>
        </view>
      </view>
    </scroll-view>
    <view class="more-btn" bindtap="onClickMore">
      <text>全\n部</text>
      <image src="{{ic_arrow_all}}" mode="widthFix" />
    </view>
  </view>
  <!-- 二级分类 -->
  <van-tree-select
    items="{{ secondClassifyList }}"
    main-active-index="{{ mainActiveIndex }}"
    height="{{classifyHeight}}"
    main-item-class="main-item-class"
    main-active-class="main-active-class"
    bind:click-nav="onClickNav"
    bind:reach-bottom="handleReachBottom"
  >
    <view slot="content" class="good-wrap">
      <!-- 排序 -->
      <view class="condition-bar">
        <text class="condition-item {{sortType === 'default' ? 'active' : ''}}" data-type="default" bind:tap="changeSort">默认排序</text>
        <view class="condition-item {{sortType === 'price' ? 'active' : ''}}" data-type="price" bind:tap="changeSort">
          <text>价格</text>
          <image class="orderby-img" src="{{priceSortImg[priceSort]}}"></image>
        </view>
      </view>
      <!-- 商品列表 -->
      <view class="merchandise-wrap">
        <merchandise
          wx:for="{{productList}}"
          subTitle
          allClassifyHorizontal
          detail="{{item}}"
          customTitle="{{'处方药不展示包装'}}"
          bind:addCart="handleaddCart"
        />
      </view>
      <view class="load-more">{{!hasMore && !noData ? '没有更多了' : ''}}</view>
      <nodata wx:if="{{noData}}"></nodata>
    </view>
  </van-tree-select>
  <!-- 全部分类弹窗 -->
  <van-popup
    show="{{showPopup}}"
    position="top"
    round
    custom-style="height: 55%;"
    bind:close="onClose"
  >
    <view class="all-classify">
      <view>
        <view class="all_text">全部分类</view>
        <scroll-view class="scroll_classify scroll_popup_view" scroll-y="true">
          <view class="popup_classify">
            <view class="classify-item popup-item {{classifyActive === item.id ? 'classify-item-active' : ''}}"
              wx:for="{{allList}}" wx:key="index" data-item="{{item}}" data-item="{{item}}" bindtap="onClickClassify">
                <image class="icon" src="{{item.icon || img_default_classification}}" />
                <view class="text">{{item.name}}</view>
            </view>
          </view>
        </scroll-view>
      </view>
      <view class="retract" catchtap="onClose">
        <view>点击收起</view>
        <image src="{{ic_arrow_up}}" mode="widthFix" />
      </view>
    </view>
  </van-popup>
  <van-toast id="van-toast" />
</view>