import Toast from '../../lib/vant-weapp/toast/toast'
const api = require('../../config/api.js')
const util = require('../../utils/util')
const app = getApp()
const ic_sort = api.ImgUrl + 'images/<EMAIL>'
const ic_sort_up = api.ImgUrl + 'images/<EMAIL>'
const ic_sort_down = api.ImgUrl + 'images/<EMAIL>'
Page({
  /**
   * 页面的初始数据
   */
  data: {
    backgroundImgUrl: api.ImgUrl + 'images/shop/bg_drug_classification.png',
    ic_input_search: api.ImgUrl + 'images/<EMAIL>',
    ic_cart: api.ImgUrl + 'images/shop/ic_cart.png',
    img_default_classification: api.ImgUrl + 'images/<EMAIL>',
    ic_arrow_all: api.ImgUrl + 'images/shop/ic_arrow_all.png',
    ic_arrow_up: api.ImgUrl + 'images/shop/ic_arrow_retract.png',
    allList: [], // 商品分类
    secondClassifyList: [],
    mainActiveIndex: 0,
    activeId: null,
    classifyHeight: 0,
    classifyActive: 0,
    showPopup: false,
    navTitle: '',
    categoryId: '',
    productList: [],
    noData: false,
    sortType: 'default',
    priceSort: 'default',
    priceSortImg: {
      default: ic_sort,
      asc: ic_sort_up,
      desc: ic_sort_down
    },
    cartNumber: null,
    page: 1,
    hasMore: true,
    scrollLeft: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.getCategoryList(options.categoryId)
    this.setData(
      {
        navTitle: options.categoryName || '商品分类',
        classifyActive: Number(options.categoryId),
        classifyHeight: app.globalData.screenHeight - app.globalData.navBarHeight - 137
      },
      () => {
        setTimeout(() => {
          this.handleScroll(options.categoryId)
        }, 100)
      }
    )
  },

  // 获取商品分类数据
  getCategoryList(id) {
    try {
      util.request(api.categoryList, {}, 'post', 1, false).then((res) => {
        if (res.data.code !== 0) {
          Toast(res.data.message)
        } else {
          const list = this.assignTextToNodes(res.data.data)
          const item = list.find((item) => item.id == id)
          const secondList = item && item.children.length > 0 ? item.children : []
          this.setData(
            {
              allList: list,
              categoryId: item.children && item.children.length > 0 ? item.children[0].id : '',
              secondClassifyList: secondList
            },
            () => {
              if (!this.data.categoryId) return
              this.getProductList()
            }
          )
        }
      })
    } catch (error) {
      throw new Error(error)
    }
  },

  // 获取商品数据
  async getProductList() {
    try {
      Toast.loading({
        message: '加载中...',
        forbidClick: true
      })
      const params = { page: this.data.page, num: 10, categoryId: this.data.categoryId, offset: 1000 }
      if (this.data.priceSort !== 'default') {
        params.orderBy = 'salePrice'
        params.order = this.data.priceSort
      }
      const response = await util.request(api.productList, params)
      const { code, data, msg } = response.data
      if (code === 0) {
        Toast.clear()
        this.setData({
          productList: this.data.page === 1 ? data.result : this.data.productList.concat(data.result),
          noData: data.result.length === 0,
          hasMore: this.data.page < data.totalPages,
          page: this.data.page + 1
        })
      } else {
        Toast.fail(msg)
      }
    } catch (error) {
      throw new Error(error)
    }
  },

  // 处理滚动到底部加载更多
  handleReachBottom() {
    if (!this.data.hasMore) {
      return // 如果正在加载或没有更多数据，则不执行任何操作
    }
    this.getProductList()
  },

  assignTextToNodes(nodes) {
    // 遍历每个节点
    nodes.forEach((node) => {
      // 为当前节点添加 text 字段，其值等于 name 字段的值
      node.text = node.name
      node.children = node.childs || []

      // 如果当前节点有子节点，递归调用函数
      if (node.children && node.children.length > 0) {
        this.assignTextToNodes(node.children)
      }
    })
    return nodes
  },

  // 点击一级分类列表
  onClickClassify(event) {
    const { id, name } = event.currentTarget.dataset.item
    this.handleScroll(id)
    const childList = this.data.allList.find((item) => item.id === id)?.children
    this.setData(
      {
        showPopup: false,
        mainActiveIndex: 0,
        classifyActive: id,
        categoryId: childList && childList.length > 0 ? childList[0].id : '',
        secondClassifyList: childList,
        page: 1,
        productList: [],
        navTitle: name,
        sortType: 'default',
        priceSort: 'default'
      },
      () => {
        if (!this.data.categoryId) return
        this.getProductList()
      }
    )
  },

  // 展开全部
  onClickMore() {
    this.setData({
      showPopup: true
    })
  },

  // 关闭弹框
  onClose() {
    this.setData({
      showPopup: false
    })
  },

  // 点击二级分类
  onClickNav({ detail = {} }) {
    this.setData(
      {
        mainActiveIndex: detail.index || 0,
        categoryId: detail.item.id,
        page: 1,
        sortType: 'default',
        priceSort: 'default',
        productList: []
      },
      () => {
        this.getProductList()
      }
    )
  },

  // 跳转搜索页
  toSearchPage() {
    wx.navigateTo({
      url: '/pages/searchMerchandise/index'
    })
  },

  // 加入购物车
  handleaddCart() {
    this.shopCartNumber()
  },

  shopCartNumber() {
    util.request(api.cartsInfo).then((res) => {
      const list = res.data.data.groups[0]?.items
      let number = null
      list &&
        list.forEach((item) => {
          number += item.quantity
        })
      this.setData({ cartNumber: number })
    })
  },

  onCart() {
    wx.navigateTo({
      url: '/pages/shoppingCart/index'
    })
  },

  // 排序
  changeSort(e) {
    const type = e.currentTarget.dataset.type
    if (type === 'price') {
      let priceSort = ''
      if (this.data.priceSort === 'default') {
        priceSort = 'asc'
      } else if (this.data.priceSort === 'asc') {
        priceSort = 'desc'
      } else if (this.data.priceSort === 'desc') {
        priceSort = 'asc'
      }
      this.setData({
        priceSort
      })
    } else {
      this.setData({
        priceSort: 'default'
      })
    }
    this.setData({
      sortType: type,
      page: 1,
      productList: []
    }, () => {
      this.getProductList()
    })
  },

  // 获取元素位置
  handleScroll(selectedId) {
    var that = this
    // 自定义组件内部使用 wx.createSelectorQuery() 需要加 in(this)
    var query = wx.createSelectorQuery().in(that) //创建节点查询器
    query.select('#item-' + selectedId).boundingClientRect() //选择id='#item-' + selectedId的节点，获取节点位置信息的查询请求
    query.select('#scroll-view').boundingClientRect() //获取滑块的位置信息
    //获取滚动位置
    query.select('#scroll-view').scrollOffset() //获取页面滑动位置的查询请求
    query.exec(function (res) {
      that.setData({
        scrollLeft: res[2].scrollLeft + res[0].left + res[0].width / 2 - res[1].width / 2
      })
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {}
})
