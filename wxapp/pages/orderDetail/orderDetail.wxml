<view class="container {{detail.status==1?'pbbtn':''}}">
  <navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
  <view class="head">
    <image src="{{imgObject.bg_ordrer}}"  class="imgBlock"></image>
    <view class="content pl30">
      <block wx:if="{{detail.status==1}}"><image src="{{imgObject.ic_order_paid}}"></image>待支付</block>
      <block wx:if="{{detail.status==2 ||detail.status==6 || detail.status==7 || detail.status==8 || detail.status==9 }}"><image src="{{imgObject.ic_order_delivered}}"></image>待发货</block>
      <block wx:if="{{detail.status==3}}"><image src="{{imgObject.ic_order_receiving}}"></image>待收货</block>
      <block wx:if="{{detail.status==4 || detail.status==5}}"><image src="{{imgObject.ic_order_complete}}"></image>已结束</block>
      <text wx:if="{{detail.status==1}}">24小时内未支付，订单将自动取消！</text>
    </view>
  </view>
  <view class="consignee flex_m p30">
    <view class="MapIcon flex_m">
      <image src="{{imgObject.ic_address}}" class="imgBlock"></image>
    </view>
    <view class="ml20">
      <view class="f28 c333 b">{{detail.receiverInfo.receiver}} <text>{{detail.receiverInfo.phone}}</text></view>
      <view class="f28 c333">{{detail.receiverInfo.province}}{{detail.receiverInfo.city}}{{detail.receiverInfo.county}}{{detail.receiverInfo.addr}}</view>
    </view>
  </view>
  <view class="mt20 bg-color-white p30">
    <view class="f28 b c333">商品信息</view>
    <view class="mt30 bb1">
      <view wx:for="{{detail.items}}" wx:key="index" class="flex_m mb20">
        <view class="img">
          <image src="{{item.icon}}" mode="aspectFill"></image>
        </view>
				<view class="flex_lr ml10 flex1">
					<view class="f28 c333 ml10 flex1">
						<text wx:if="{{item.rx === 1}}" class="tag">Rx</text>{{item.name}}
					</view>
					<view class="flex_tb">
						<view class="f28 c333 b flex_c_end">
							¥{{item.price}}
						</view>
						<view class="f28 c999 flex_c_end">
							x{{item.quantity}}
						</view>
					</view>
				</view>
      </view>
    </view>
    <view class="bb1 pt30 pb30">
      <view class="flex_lr_m"> <text class="f28 c999">商品金额</text>  <text class="f28 c333">¥{{detail.totalAmount}}</text></view>
      <view class="flex_lr_m"><text class="f28 c999">运费</text>  <text class="f28 c333">¥{{detail.freight}}</text></view>
    </view>
    <view class="tr pt30 c333 f28">
      总计：<text class="f28 b color-danger">¥{{detail.realPay}}</text>
    </view>
  </view>
  <view class="mt20 p30 bg-color-white">
    <view class="pb20 c999 f28">订单编号 <text class="fr c333">{{detail.orderSn}}</text></view>
    <view class="pb20 c999 f28">下单时间 <text class="fr c333">{{detail.orderTime}}</text></view>
    <view class="pb20 c999 f28">配送方式 <text class="fr c333">{{detail.carrier || '-'}}</text></view>
    <view class="pb20 c999 f28">支付方式 <text class="fr c333">{{detail.payType}}</text></view>
    <view class="c999 f28 flex_lr"><text class="c999 f28">订单备注</text> <text class="c333 flex1 ml50 tr f28">{{detail.remark || '-'}}</text></view>
  </view>
</view>
<view class="fixed b0 l0 w100 bg-color-white pl30 pr30 confir flex_c_end">
    <view class="fixed_btn flex_c_m" bindtap="onContact" data-item='{{detail}}'>联系{{detail.serviceHotline ? '售后' : '平台'}}</view>
    <view wx:if="{{detail.status==1}}" class="fixed_btn flex_c_m" bindtap="handleCancel">取消订单</view>
    <view wx:if="{{detail.status==1}}" class="fixed_btn flex_c_m" bindtap="handlePay">支付</view>
</view>
<van-action-sheet
  show="{{ show }}"
  actions="{{ actions }}"
  safe-area-inset-bottom="{{true}}"
  safeAreaInsetBottom="{{true}}"
  cancelText="取消"
  title="工作时间：10:00-19:00"
  bind:close="onClose"
  bind:select="onSelect"
  bind:cancel="onClose"
/>
