<wxs src="./shop.wxs" module="utils" />

<view class="page page-wrap" style="background-image: {{imgObject.backgroundImgUrl}}">
    <view class="mod2" style="margin-top:{{ MenuButtonHight - 48}}px;"></view>
    <view class="box1">
        <!-- title -->
        <view class="mod2">
            <view class="section1">
                <text lines="1" class="word2">药房</text>
            </view>
        </view>
        <!-- 搜索 -->
        <view class="mod3" bind:tap="goSearch">
            <merchandiseSearch readonly  background="transparent" shape="round"></merchandiseSearch>
        </view>
        <!-- 分类区域 -->
        <view class="mod4">
            <classify id="classify"></classify>
        </view>
        <!-- banner -->
        <view class="banner rel" wx:if="{{bannerList.length > 0}}">
			<swiper class="swiper_1" indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}" interval="{{interval}}" duration="{{duration}}"
				indicator-color="{{indicatorColor}}" indicator-active-color="{{indicatorActiveColor}}" circular="{{circular}}"
				bindchange="swiperChange">
				<block wx:for="{{bannerList}}" wx:key="index" >
					<swiper-item data-type="{{item.bannerType}}" data-id="{{item.id}}" data-url="{{item.targetUrl}}" data-content="{{item.content}}" data-video="{{item.videoId}}" catch:tap="handleDetail">
						<image src="{{item.bannerUrl}}" mode="widthFix" class="imgBlock" />
					</swiper-item>
				</block>
			</swiper>
			<view class="dots">
				<block wx:for="{{imgUrls}}" wx:key="index">
					<view class="dot{{index == bannerCurrent ? ' active' : ''}}"></view>
				</block>
			</view>
		</view>
        <!-- 用药专区 -->
        <view class="bg-white" wx:if="{{zoneList.length > 0}}">
            <view class="flex">
                <view class="zone_title">用药专区</view>
                <navigator open-type="navigate" url="/pages/zoneDetail/list/index" hover-class="none" class="flex mr20">
					<view class="more-text">查看更多</view>
                    <!-- <view class="rigth-icon c999 mt1">
					</view> -->
					<image class="right-icon" src="{{imgObject.ic_my_more}}"></image>
				</navigator>
            </view>
            <scroll-view class="scroll-view-horizontal" scroll-x="true">
                <view class="zone_item" wx:for="{{zoneList}}" wx:key="id" data-id="{{ item.id }}"
                    data-title="{{ item.mainTitle }}"
                    bindtap="toZoneDetail">
                    <view class="zone_item_img">
                        <image src="{{ item.thumb }}" mode="widthFix" />
                    </view>
                    <view class="zone_item_txt">{{ item.mainTitle }}</view>
                </view>
            </scroll-view>
        </view>
        <!-- 家庭常备 -->
        <view
           class="special_subject"
           wx:for="{{mallSections}}"
           wx:key="sectionIndex"
           wx:for-item="section"
           wx:for-index="sectionIndex"
        >
            <view class="special_subject_title">
                <text class="main_title">{{section.mainTitle}}</text>
                <text class="sub_title">{{section.subTitle}}</text>
            </view>
            <view class="special_subject_banner" wx:if="{{section.banner}}">
                <image
                    src="{{section.banner}}"
                    mode="widthFix"
                />
            </view>
            <swiper
               bindchange="swiperChange"
               indicator-color="#EEEEEE"
               indicator-active-color="#367DFF"
            >
                <swiper-item wx:for="{{utils.getLen(section.products.length)}}" wx:for-item="swiperItem" wx:for-index="swiperItemIndex" wx:key="swiperItemIndex">
                    <van-grid column-num="3" gutter="{{ 8 }}" border="{{false}}">
                        <van-grid-item
                           use-slot
                           content-class="content-class"
                           wx:for="{{utils.getArr(section.products, swiperItemIndex)}}"
                           wx:for-item="goods"
                           wx:for-index="goodsIndex"
                           wx:key="goodsIndex"
                        >
                            <merchandise detail="{{goods}}" size="mid" showCar="{{false}}"></merchandise>
                        </van-grid-item>
                    </van-grid>
                </swiper-item>
            </swiper>
            <view class="swiper-dot">
                <view
                   wx:for="{{utils.getLen(section.products.length)}}"
                   wx:for-index="dotIndex"
                   wx:key="dotIndex"
                   class="swiper-dot-item {{dotIndex === swiperCurrent ? 'active' : ''}}"
                >
                </view>
            </view>
        </view>
        <!-- 精选药品 -->
        <text lines="1" class="txt3">精选推荐</text>
        <van-grid column-num="2" gutter="{{ 10 }}" border="{{false}}">
            <van-grid-item use-slot content-class="content-class" wx:for="{{featuredList}}" wx:key="index">
                <merchandise detail="{{item}}" otcConsultBuying="{{otcConsultBuying}}" bind:addCart="handleaddCart"></merchandise>
            </van-grid-item>
        </van-grid>
        <!-- <waterfall medicines="{{featuredList}}" bind:addCart="handleaddCart"></waterfall> -->
        <view class="load-more">{{hasMore ? '正在加载...' : ''}}</view>
        <nodata wx:if="{{featuredList.length === 0}}"></nodata>
    </view>
</view>
<fixedCart id="fixedCart"></fixedCart>
