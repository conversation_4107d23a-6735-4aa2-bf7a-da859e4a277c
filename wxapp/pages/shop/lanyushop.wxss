page {
  background: #f8f8f8;
}
.page {
  position: relative;
  height: auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: url("https://patient-pro.naiterui.com/images/img_shopping_mall.png") no-repeat;
}
.page-wrap {
  background-size: contain;
}
.box1 {
  flex-direction: column;
  position: relative;
}
.mod1 {
  height: 88rpx;
  background-color: rgba(255,255,255,1);
  display: flex;
  flex-direction: column;
  width: 750rpx;
}
.outer1 {
  height: 88rpx;
  display: flex;
  flex-direction: column;
  width: 750rpx;
}
.outer2 {
  width: 680rpx;
  height: 34rpx;
  flex-direction: row;
  display: flex;
  margin: 32rpx 0 0 42rpx;
}
.box2 {
  width: 108rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  text-align: center;
  white-space: nowrap;
  font-size: 0rpx;
}
.word1 {
  font-size: 28rpx;
  font-family: Helvetica;
  color: rgba(0,0,0,1.000000);
  line-height: 34rpx;
  overflow: hidden;
  text-overflow: ellipsis;
}
.info1 {
  font-size: 28rpx;
  font-family: Helvetica;
  color: rgba(0,0,0,1.000000);
  line-height: 34rpx;
  overflow: hidden;
  text-overflow: ellipsis;
}
.box3 {
  width: 34rpx;
  height: 22rpx;
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng8ce7767a969f0a87db754c9e45a3e7a4dcead05688664c56b6305e19b5138508) -2rpx -2rpx no-repeat;
  background-size: 36rpx 24rpx;
  display: flex;
  flex-direction: column;
  margin: 4rpx 0 0 438rpx;
}
.box4 {
  width: 32rpx;
  height: 22rpx;
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng92d71d13da58ba4887551315bf83a1d7044dd8e56d5d54f8f479185caa02c6e2) -2rpx 0rpx no-repeat;
  background-size: 32rpx 24rpx;
  display: flex;
  flex-direction: column;
  margin: 4rpx 0 0 10rpx;
}
.icon1 {
  width: 50rpx;
  height: 24rpx;
  margin: 2rpx 0 0 8rpx;
}
.mod2 {
  height: 88rpx;
  display: flex;
  flex-direction: column;
}
.section1 {
  width: 380rpx;
  height: 64rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 12rpx 0 0 342rpx;
}
.word2 {
  width: 64rpx;
  height: 44rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 32rpx;
  font-family: PingFang-SC-Bold;
  white-space: nowrap;
  line-height: 44rpx;
  text-align: center;
  margin-top: 10rpx;
  font-weight: 700;
}
.img1 {
  width: 176rpx;
  height: 64rpx;
}
.mod3 {

}
.box5 {
  height: 80rpx;
  border-radius: 8rpx;
  border: 0.5px solid rgba(223,223,223,1);
  background-color: rgba(255,255,255,1);
  display: flex;
  flex-direction: column;
  width: 710rpx;
  margin: 12rpx 0 0 20rpx;
}
.block1 {
  width: 418rpx;
  height: 40rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 20rpx 0 0 20rpx;
}
.icon2 {
  width: 40rpx;
  height: 40rpx;
}
.word3 {
  width: 364rpx;
  height: 40rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(180,180,180,1);
  font-size: 28rpx;
  font-family: PingFangSC-Regular;
  white-space: nowrap;
  line-height: 40rpx;
  text-align: left;
}
.bd1 {
  width: 654rpx;
  height: 296rpx;
  flex-wrap: wrap;
  display: flex;
  flex-direction: row;
  margin: 40rpx 0 0 48rpx;
}
.bd2 {
  width: 96rpx;
  height: 138rpx;
  display: flex;
  flex-direction: column;
  margin: 0 44rpx 20rpx 0;
  justify-content: space-between;
}
.main2 {
  z-index: 7;
  position: relative;
  width: 92rpx;
  height: 92rpx;
  border-radius: 100%;
  overflow: hidden;
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng165c766473823384b4db9ac224d9b22d2f88124a744a733dce7aca452745c7f8) 100% no-repeat;
  margin-left: 2rpx;
  display: flex;
  flex-direction: column;
}
.mod5 {
  z-index: 8;
  position: absolute;
  left: 8rpx;
  top: 26rpx;
  width: 76rpx;
  height: 60rpx;
  box-shadow: 0px 2px 3px 0px rgba(207,207,207,0.47);
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng1ff5d4c7686a97c971329f7c35e9a61114d7f632e44bcfcf7376e02e87613214) -6rpx -2rpx no-repeat;
  background-size: 88rpx 70rpx;
  display: flex;
  flex-direction: column;
}
.word4 {
  width: 96rpx;
  height: 34rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(102,102,102,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  white-space: nowrap;
  line-height: 34rpx;
  text-align: center;
  margin-top: 12rpx;
}
.bd3 {
  width: 96rpx;
  height: 138rpx;
  display: flex;
  flex-direction: column;
  margin: 0 44rpx 20rpx 0;
  justify-content: space-between;
}
.block2 {
  z-index: 12;
  position: relative;
  width: 92rpx;
  height: 92rpx;
  border-radius: 100%;
  overflow: hidden;
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng165c766473823384b4db9ac224d9b22d2f88124a744a733dce7aca452745c7f8) 100% no-repeat;
  margin-left: 2rpx;
  display: flex;
  flex-direction: column;
}
.box6 {
  z-index: 13;
  position: absolute;
  left: 2rpx;
  top: 30rpx;
  width: 88rpx;
  height: 52rpx;
  box-shadow: 0px 3px 4px 0px rgba(204,204,204,0.5);
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngf86e4e6282d9cc3444ea42941d0bd35154f06289d42e6be56c0288514bbdb52e) -2rpx -2rpx no-repeat;
  background-size: 92rpx 66rpx;
  display: flex;
  flex-direction: column;
}
.info2 {
  width: 96rpx;
  height: 34rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(102,102,102,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  white-space: nowrap;
  line-height: 34rpx;
  text-align: center;
  margin-top: 12rpx;
}
.bd4 {
  width: 96rpx;
  height: 138rpx;
  display: flex;
  flex-direction: column;
  margin: 0 42rpx 20rpx 0;
  justify-content: space-between;
}
.outer3 {
  z-index: 17;
  position: relative;
  width: 92rpx;
  height: 92rpx;
  border-radius: 100%;
  overflow: hidden;
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng165c766473823384b4db9ac224d9b22d2f88124a744a733dce7aca452745c7f8) 100% no-repeat;
  margin-left: 2rpx;
  display: flex;
  flex-direction: column;
}
.wrap1 {
  z-index: 18;
  position: absolute;
  left: 6rpx;
  top: 28rpx;
  width: 80rpx;
  height: 56rpx;
  box-shadow: 0px 2px 4px 0px rgba(188,188,188,0.5);
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng792f53b87c6075cc7d25f7f60eeb41d8600b9c953e27562f29a7776330ed4a2e) -6rpx -4rpx no-repeat;
  background-size: 92rpx 68rpx;
  display: flex;
  flex-direction: column;
}
.word5 {
  width: 96rpx;
  height: 34rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(102,102,102,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  white-space: nowrap;
  line-height: 34rpx;
  text-align: center;
  margin-top: 12rpx;
}
.bd5 {
  width: 96rpx;
  height: 138rpx;
  display: flex;
  flex-direction: column;
  margin: 0 44rpx 20rpx 0;
  justify-content: space-between;
}
.outer4 {
  z-index: 22;
  position: relative;
  width: 92rpx;
  height: 92rpx;
  border-radius: 100%;
  overflow: hidden;
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng165c766473823384b4db9ac224d9b22d2f88124a744a733dce7aca452745c7f8) 100% no-repeat;
  margin-left: 2rpx;
  display: flex;
  flex-direction: column;
}
.box7 {
  z-index: 23;
  position: absolute;
  left: 6rpx;
  top: 38rpx;
  width: 80rpx;
  height: 32rpx;
  box-shadow: 0px 2px 4px 0px rgba(188,188,188,0.5);
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPnge79574df0afa70cb7865ba7253e3da55b46ee0fa617b2c9e3d1b43c413c11c2f) -6rpx -4rpx no-repeat;
  background-size: 92rpx 48rpx;
  display: flex;
  flex-direction: column;
}
.word6 {
  width: 96rpx;
  height: 34rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(102,102,102,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  white-space: nowrap;
  line-height: 34rpx;
  text-align: center;
  margin-top: 12rpx;
}
.bd6 {
  width: 96rpx;
  height: 138rpx;
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.outer5 {
  z-index: 27;
  position: relative;
  width: 92rpx;
  height: 92rpx;
  border-radius: 100%;
  overflow: hidden;
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng165c766473823384b4db9ac224d9b22d2f88124a744a733dce7aca452745c7f8) 100% no-repeat;
  margin-left: 2rpx;
  display: flex;
  flex-direction: column;
}
.box8 {
  z-index: 28;
  position: absolute;
  left: 6rpx;
  top: 32rpx;
  width: 80rpx;
  height: 52rpx;
  box-shadow: 0px 2px 4px 0px rgba(188,188,188,0.5);
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngb5a0a93110e65fbb9f6cdfb8ba825a2313559b4533d5273659e1588d83cd37a4) -6rpx -4rpx no-repeat;
  background-size: 92rpx 64rpx;
  display: flex;
  flex-direction: column;
}
.txt1 {
  width: 96rpx;
  height: 34rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(102,102,102,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  white-space: nowrap;
  line-height: 34rpx;
  text-align: center;
  margin-top: 12rpx;
}
.bd7 {
  width: 96rpx;
  height: 138rpx;
  display: flex;
  flex-direction: column;
  margin: 0 44rpx 20rpx 0;
  justify-content: space-between;
}
.outer6 {
  z-index: 32;
  position: relative;
  width: 92rpx;
  height: 92rpx;
  border-radius: 100%;
  overflow: hidden;
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng165c766473823384b4db9ac224d9b22d2f88124a744a733dce7aca452745c7f8) 100% no-repeat;
  margin-left: 2rpx;
  display: flex;
  flex-direction: column;
}
.mod6 {
  z-index: 33;
  position: absolute;
  left: 16rpx;
  top: 20rpx;
  width: 60rpx;
  height: 80rpx;
  box-shadow: 0px 3px 4px 0px rgba(159,159,159,0.5);
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngaae042dee24638c42720cd8ac58a3259ca6b01016a92a60bb4e8b34d8f1c376f) -10rpx -2rpx no-repeat;
  background-size: 78rpx 74rpx;
  display: flex;
  flex-direction: column;
}
.info3 {
  width: 96rpx;
  height: 34rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(102,102,102,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  white-space: nowrap;
  line-height: 34rpx;
  text-align: center;
  margin-top: 12rpx;
}
.bd8 {
  width: 96rpx;
  height: 138rpx;
  display: flex;
  flex-direction: column;
  margin: 0 44rpx 20rpx 0;
  justify-content: space-between;
}
.main3 {
  z-index: 37;
  position: relative;
  width: 92rpx;
  height: 92rpx;
  border-radius: 100%;
  overflow: hidden;
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng165c766473823384b4db9ac224d9b22d2f88124a744a733dce7aca452745c7f8) 100% no-repeat;
  margin-left: 2rpx;
  display: flex;
  flex-direction: column;
}
.layer1 {
  z-index: 38;
  position: absolute;
  left: 6rpx;
  top: 36rpx;
  width: 80rpx;
  height: 44rpx;
  box-shadow: 0px 2px 4px 0px rgba(199,197,197,0.5);
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng2e998d27640c8795c78ab201beab78877d6a7f384fb7936655d7e24b90029827) -6rpx -6rpx no-repeat;
  background-size: 92rpx 60rpx;
  display: flex;
  flex-direction: column;
}
.info4 {
  width: 96rpx;
  height: 34rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(102,102,102,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  white-space: nowrap;
  line-height: 34rpx;
  text-align: center;
  margin-top: 12rpx;
}
.bd9 {
  width: 96rpx;
  height: 138rpx;
  display: flex;
  flex-direction: column;
  margin: 0 42rpx 20rpx 0;
  justify-content: space-between;
}
.main4 {
  z-index: 42;
  position: relative;
  width: 92rpx;
  height: 92rpx;
  border-radius: 100%;
  overflow: hidden;
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng165c766473823384b4db9ac224d9b22d2f88124a744a733dce7aca452745c7f8) 100% no-repeat;
  margin-left: 2rpx;
  display: flex;
  flex-direction: column;
}
.block3 {
  z-index: 43;
  position: absolute;
  left: 12rpx;
  top: 18rpx;
  width: 68rpx;
  height: 6rpx;
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngcc55e3f26d1e5b18a5800d3c9a602b874fba0a3acd5445f9764f705dfcfbda0d) 0rpx 0rpx no-repeat;
  background-size: 68rpx 6rpx;
  display: flex;
  flex-direction: column;
}
.block4 {
  z-index: 44;
  position: absolute;
  left: 12rpx;
  top: 18rpx;
  width: 68rpx;
  height: 92rpx;
  box-shadow: 0px 3px 4px 0px rgba(159,159,159,0.5);
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngb2da0cf05ce369d464d495317d5fa30569c6707a9a2c5e75202183324ed21f88) -8rpx -2rpx no-repeat;
  background-size: 84rpx 76rpx;
  display: flex;
  flex-direction: column;
}
.info5 {
  width: 96rpx;
  height: 34rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(102,102,102,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  white-space: nowrap;
  line-height: 34rpx;
  text-align: center;
  margin-top: 12rpx;
}
.bd10 {
  width: 96rpx;
  height: 138rpx;
  display: flex;
  flex-direction: column;
  margin: 0 44rpx 20rpx 0;
  justify-content: space-between;
}
.outer7 {
  z-index: 48;
  position: relative;
  width: 92rpx;
  height: 92rpx;
  border-radius: 100%;
  overflow: hidden;
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng165c766473823384b4db9ac224d9b22d2f88124a744a733dce7aca452745c7f8) 100% no-repeat;
  margin-left: 2rpx;
  display: flex;
  flex-direction: column;
}
.box9 {
  z-index: 49;
  position: absolute;
  left: 10rpx;
  top: 28rpx;
  width: 72rpx;
  height: 56rpx;
  box-shadow: 0px 3px 4px 0px rgba(200,200,200,0.5);
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngf09124fe063615c6f859ca2651d0d88cb2082ed4e8f62265f4c9c79e5935a3fc) -8rpx -2rpx no-repeat;
  background-size: 88rpx 68rpx;
  display: flex;
  flex-direction: column;
}
.txt2 {
  width: 96rpx;
  height: 34rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(102,102,102,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  white-space: nowrap;
  line-height: 34rpx;
  text-align: center;
  margin-top: 12rpx;
}
.bd11 {
  width: 96rpx;
  height: 138rpx;
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.icon3 {
  width: 92rpx;
  height: 92rpx;
  margin-left: 2rpx;
}
.word7 {
  width: 96rpx;
  height: 34rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(102,102,102,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  white-space: nowrap;
  line-height: 34rpx;
  text-align: center;
  margin-top: 12rpx;
}
.txt3 {
  width: 144rpx;
  height: 50rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 36rpx;
  font-family: PingFang-SC-Bold;
  white-space: nowrap;
  line-height: 50rpx;
  text-align: left;
  margin-left: 20rpx;
  margin-bottom: 30rpx;
  font-weight: 700;
}
.mod7 {
  width: 710rpx;
  height: 520rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 20rpx 0 0 20rpx;
}

.block5 {
  width: 344rpx;
  height: 500rpx;
  display: flex;
  flex-direction: column;
}
.block6 {
  height: 344rpx;
  border-radius: NaNrpx;
  background-color: rgba(255,255,255,1);
  display: flex;
  flex-direction: column;
  width: 344rpx;
}
.img2 {
  width: 236rpx;
  height: 208rpx;
  margin: 68rpx 0 0 54rpx;
}
.paragraph1 {
  width: 308rpx;
  height: 80rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 28rpx;
  font-family: PingFangSC-Regular;
  line-height: 40rpx;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 20rpx 0 0 20rpx;
}
.block7 {
  width: 104rpx;
  height: 44rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 12rpx 0 0 20rpx;
}
.txt5 {
  width: 16rpx;
  height: 34rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(240,85,66,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  white-space: nowrap;
  line-height: 34rpx;
  text-align: left;
  margin-top: 8rpx;
}
.txt6 {
  width: 86rpx;
  height: 44rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(240,85,66,1);
  font-size: 32rpx;
  font-family: PingFang-SC-Bold;
  white-space: nowrap;
  line-height: 44rpx;
  text-align: left;
}
.mod8 {
  width: 710rpx;
  height: 352rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 20rpx 0 0 20rpx;
}
.box10 {
  height: 352rpx;
  border-radius: 8rpx;
  background-color: rgba(255,255,255,1);
  display: flex;
  flex-direction: column;
  width: 344rpx;
}
.outer8 {
  height: 344rpx;
  border-radius: NaNrpx;
  background-color: rgba(255,255,255,1);
  display: flex;
  flex-direction: column;
  width: 344rpx;
}
.pic2 {
  width: 220rpx;
  height: 220rpx;
  margin: 62rpx 0 0 62rpx;
}
.box11 {
  height: 352rpx;
  border-radius: 8rpx;
  background-color: rgba(255,255,255,1);
  display: flex;
  flex-direction: column;
  width: 344rpx;
}
.main12 {
  z-index: 91;
  height: 344rpx;
  border-radius: NaNrpx;
  background-color: rgba(255,255,255,1);
  display: flex;
  flex-direction: column;
  width: 344rpx;
  position: relative;
}
.img3 {
  width: 260rpx;
  height: 260rpx;
  margin: 42rpx 0 0 42rpx;
}
.layer2 {
  z-index: 199;
  position: absolute;
  left: 314rpx;
  top: 44rpx;
  width: 28rpx;
  height: 28rpx;
  background-color: rgba(254,33,42,1);
  border-radius: 100%;
  display: flex;
  flex-direction: column;
}
.word8 {
  z-index: 200;
  position: absolute;
  left: 324rpx;
  top: 42rpx;
  width: 10rpx;
  height: 28rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1);
  font-size: 20rpx;
  font-family: PingFangSC-Regular;
  white-space: nowrap;
  line-height: 28rpx;
  text-align: left;
}
.layer3 {
  z-index: 187;
  position: absolute;
  left: 246rpx;
  top: 34rpx;
  width: 88rpx;
  height: 88rpx;
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngf29e6e201b96da0065f99cb98ad5e0d73287537e214d112c43a4856c4d396892) -10rpx -10rpx no-repeat;
  background-size: 108rpx 108rpx;
  display: flex;
  flex-direction: column;
}
.main13 {
  z-index: 143;
  height: 166rpx;
  background-color: rgba(255,255,255,1);
  display: flex;
  flex-direction: column;
  width: 750rpx;
  position: absolute;
  left: 0rpx;
  top: 1458rpx;
}
.bd12 {
  width: 750rpx;
  height: 166rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.bd13 {
  height: 98rpx;
  background-color: rgba(255,255,255,1);
  display: flex;
  flex-direction: column;
  width: 750rpx;
}
.mod9 {
  width: 750rpx;
  height: 90rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.box12 {
  width: 750rpx;
  height: 2rpx;
  background-color: rgba(238,238,238,1);
  display: flex;
  flex-direction: column;
}
.box13 {
  width: 610rpx;
  height: 48rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 6rpx 0 0 70rpx;
}
.label1 {
  width: 48rpx;
  height: 48rpx;
}
.label2 {
  width: 48rpx;
  height: 48rpx;
}
.label3 {
  width: 48rpx;
  height: 48rpx;
}
.icon4 {
  width: 48rpx;
  height: 48rpx;
}
.box14 {
  width: 602rpx;
  height: 28rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 6rpx 0 0 74rpx;
}
.txt7 {
  width: 40rpx;
  height: 28rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(180,180,180,1);
  font-size: 20rpx;
  font-family: PingFang-SC-Bold;
  white-space: nowrap;
  line-height: 28rpx;
  text-align: left;
}
.word9 {
  width: 40rpx;
  height: 28rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(180,180,180,1);
  font-size: 20rpx;
  font-family: PingFang-SC-Bold;
  white-space: nowrap;
  line-height: 28rpx;
  text-align: left;
}
.word10 {
  width: 40rpx;
  height: 28rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(54,125,255,1);
  font-size: 20rpx;
  font-family: PingFang-SC-Bold;
  white-space: nowrap;
  line-height: 28rpx;
  text-align: left;
}
.word11 {
  width: 40rpx;
  height: 28rpx;
  display: block;
  overflow-wrap: break-word;
  color: rgba(180,180,180,1);
  font-size: 20rpx;
  font-family: PingFang-SC-Bold;
  white-space: nowrap;
  line-height: 28rpx;
  text-align: left;
}
.pic3 {
  width: 750rpx;
  height: 68rpx;
}
.content-class {
  padding: 0 !important;
  background: transparent !important;
}
.special_subject {
  margin: 20rpx 20rpx 28rpx;
  background-image: url("https://patient-pro.naiterui.com/images/img_special_subject.png");
  background-size: cover;
  padding-bottom: 20rpx;

}
.special_subject .special_subject_title {
  padding: 32rpx 16rpx 16rpx 16rpx;
  font-size: 36rpx;
  font-family: FontName;
  color: #2A54A0;
  line-height: 21px;
  display: flex;
  align-items: center;
  font-weight: 700;
}
.special_subject .special_subject_title .sub_title {
  font-size: 24rpx;
  font-family: PingFangSC, PingFang SC;
  color: #5075B5;
  line-height: 34rpx;
  margin-left: 40rpx;
  font-weight: 700;
}
swiper {
  height: 343rpx;
}
.swiper-dot {
  display: flex;
  align-items: center;
  justify-content: center;
}
.swiper-dot-item {
  margin-top: 10rpx;
  width: 12rpx;
  height: 6rpx;
  background: #EEEEEE;
  border-radius: 4rpx;
  margin-left: 4rpx;
}
.swiper-dot-item.active {
  width: 20rpx;
  background: #367DFF;
}
.load-more {
  text-align: center;
  margin: 20rpx auto;
  font-size: 26rpx;
  color: #aaa;
}
.mod4 {
  margin-top: 22rpx;
}
.mod3_label {
  display: flex;
  justify-content: space-evenly;
  margin-top: 24rpx;
}
.mod_icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 4rpx;
}
.mod3_title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 24rpx;
  color: #D1A45F;
}
.mod3_label_item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 158rpx;
  background: rgba(255,250,243,0.5);
  border-radius: 26rpx;
  border: 1rpx solid #F6E9D7;
  padding: 6rpx 0!important;
}
.banner {
  margin: 40rpx auto;
  width: 710rpx;
  height: 240rpx;
}
.dots {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 20rpx;
  display: flex;
  justify-content: center;
}

.dots .dot {
  margin: 0 8rpx;
  width: 10rpx;
  height: 10rpx;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  transition: all 0.6s;
}

.dots .dot.active {
  background: #2e9cff;
  width: 20rpx;
  height: 10rpx;
  border-radius: 6rpx;
  background: #ffffff;
}

.swiper_1 {
  height: 100%;
}

.bg-white {
  margin: 20rpx;
  padding: 28rpx 0rpx 20rpx 16rpx;
  background: #ffffff;
  border-radius: 20rpx;
}

.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-icon {
  width: 20rpx;
  height: 44rpx;
  display: block;
}

.more-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #8b8b8b;
}

.bg-white .zone_title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 36rpx;
  color: #333333;
}

.scroll-view-horizontal {
  white-space: nowrap;
  overflow-x: auto;
  margin-top: 24rpx;
}

.zone_item {
  display: inline-block;
  margin-right: 20rpx;
}

.zone_item_img image {
  width: 228rpx !important;
  height: 160rpx !important;
  border-radius: 8rpx;
  display: block;
}

.zone_item_txt {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 28rpx;
  color: #333333;
  margin-top: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 228rpx;
}

.special_subject_banner {
  margin: 0 16rpx!important;
  border-radius: 8rpx!important;
  overflow: hidden!important;
  height: 180rpx;
}

.special_subject_banner image {
  width: 100%!important;
  height: 100%!important;
}