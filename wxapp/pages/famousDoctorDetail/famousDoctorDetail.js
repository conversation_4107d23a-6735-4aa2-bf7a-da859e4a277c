// pages/doctorDetail/doctorDetal.js
var api = require('../../config/api.js')
var util = require('../../utils/util.js')
const app = getApp()
Page({
  /**
	 * 页面的初始数据
	 */
  data: {
    isBack: true,
    navTitle: '',
    textHidden: true,
    serverActive: 1,
    backgroundColor: '',
    imgObject: {
      bj: api.ImgUrl + 'images/bg_my.png',
      // ic_certified: api.ImgUrl + 'images/ic_certified.png',
      ic_certified_new: api.ImgUrl + 'images/<EMAIL>',
      ic_open: api.ImgUrl + 'images/<EMAIL>',
      ic_doctor_image: api.ImgUrl + 'images/ic_doctor_image.png',
      ic_doctor_video: api.ImgUrl + 'images/ic_doctor_video.png',
      ic_doctor_choice: api.ImgUrl + 'images/ic_doctor_choice.png',
      ic_doctor_more: api.ImgUrl + 'images/ic_doctor_more.png',
      ic_star_bright: api.ImgUrl + 'images/ic_star_bright.png',
      ic_star_grey: api.ImgUrl + 'images/ic_star_grey.png',
      bg_authentication: api.ImgUrl + 'images/bg_authentication.png',
      ic_doctor_authentication: api.ImgUrl + 'images/ic_doctor_authentication.png',
      ic_name: api.ImgUrl + 'images/ic_name.png',
      ic_practice: api.ImgUrl + 'images/ic_practice.png',
      ic_qualification: api.ImgUrl + 'images/ic_qualification.png',
      ic_success: api.ImgUrl + 'images/ic_success.png'
    },
    sourceType: null, //1.图文咨询 2.视频复诊 3.我的医生
    eval: [],
    cityList: null,
    doctor: {},
    authShow: false,
    clickFlag: true,
    tapTime: '',
    openView: false
  },

  // onClose() {
  //   this.setData({
  //     authShow: false,
  //     clickFlag: true
  //   })
  //   this.SeeFun(this.data.type, this.data.doctorId, this)
  // },
  // authSub() {
  //   var that = this
  //   wx.requestSubscribeMessage({
  //     tmplIds: app.globalData.templateId,
  //     success: () => {
  //       that.SeeFun(that.data.type, that.data.doctorId, that)
  //     },
  //     fail(res) {
  //       that.SeeFun(that.data.type, that.data.doctorId, that)
  //     }
  //   })
  // },
  // 发起问诊
  handleConsult(e) {
    var nowTime = new Date()
    if (nowTime - this.data.tapTime < 2000) {
      return
    }
    this.setData({
      doctorName: e.currentTarget.dataset.name,
      doctorId: e.currentTarget.dataset.id,
      type: e.currentTarget.dataset.type * 1,
      tapTime: nowTime
    })
    this.authToast.seeDoctor(e)
  },
  onAuthSub() {
    // if (!this.authToast.data.isSwatchOff) {
    //   wx.requestSubscribeMessage({
    //     tmplIds: app.globalData.templateId,
    //     success: () => {
    //       this.authToast.SeeFun(this.data.type, this.data.doctorId)
    //     },
    //     fail: (res) => {
    //       console.log('onAuthSub', 'fail')
    //       this.authToast.SeeFun(this.data.type, this.data.doctorId)
    //     }
    //   })
    // } else {
      wx.openSetting({
        success: (res) => {
          this.authToast.setData({
            isSwatchOff: false,
            authShow: false,
            clickFlag: true
          })
        }
      })
    // }
  },
  onClose() {
    this.authToast.setData({
      authShow: false,
      clickFlag: true
    })
    this.authToast.SeeFun(this.data.type, this.data.doctorId)
  },
  // 查看更多评论
  evalMore() {
    wx.navigateTo({
      url: '/pages/evalList/evalList?id=' + this.data.doctor.doctorId
    })
  },
  // 显示所有
  textShowFun: function() {
    this.setData({
      textHidden: !this.data.textHidden
    })
  },
  serveFun: function(e) {
    var id = e.currentTarget.dataset.id
    if (this.data.sourceType != 3) {
      return
    }
    this.setData({
      type: id,
      serverActive: id
    })
  },

  // 我的医生详情
  getDoctorDetail() {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    const apiUrl = this.data.type === 1 ? api.doctorDetail : api.clinicDetail
    util.request(apiUrl, {
      doctorId: this.data.doctorId
    }, 'post', 2, false)
      .then(res => {
        util.hideToast()
        if (res.data.code === 0) {
          this.setData({
            doctor: res.data.data
          })
        } else {
          util.showToast({
            icon: 'none',
            title: res.data.msg
          })
        }
      })
      .catch(res => {
      })
  },

  onClickShow() {
    this.setData({
      openView: true
    })
  },
  onClickHide() {
    this.setData({
      openView: false
    })
  },

  /**
	 * 生命周期函数--监听页面加载
	 */
  onLoad: function(options) {
    console.log(options)
    this.setData({
      sourceType: options.type,
      navTitle: options.type === 2 ? '' : '医生详情',
      backgroundColor: options.type * 1 === 2 ? '' : '#fff',
      doctorId: options.doctorId,
      serverActive: options.type == 1 ? 2 : 1
    })

  },

  /**
	 * 生命周期函数--监听页面显示
	 */
  onShow: function() {
    console.log(this, 150)
    this.authToast = this.selectComponent('#authToast')
    console.log(this.authToast, 151)
    this.getDoctorDetail()
  },

  /**
	 * 生命周期函数--监听页面隐藏
	 */
  onHide: function() {

  },

  /**
	 * 生命周期函数--监听页面卸载
	 */
  onUnload: function() {

  },

  /**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
  onPullDownRefresh: function() {

  },

  /**
	 * 页面上拉触底事件的处理函数
	 */
  onReachBottom: function() {

  }

  /**
	 * 用户点击右上角分享
	 */
  // onShareAppMessage: function() {

  // }

})
