<!--pages/doctorDetail/doctorDetal.wxml-->
<view class="container rel" style="padding-bottom:128rpx;" wx:if='{{doctor.department}}'>
	<navbar isBack="{{isBack}}" home="{{!isBack}}"></navbar>
	<view class="bg abs">
		<image src="{{imgObject.bj}}" class="w100" mode="widthFix"></image>
	</view>
	<view class="main doctorMain">
		<view class="bg-color-white p20 rel doctorInfo m20">
			<image src="{{imgObject.ic_certified_new}}" class="icon ml20" catchtap="onClickShow"></image>
			<view class="photo">
				<image src="{{doctor.photo ? doctor.photo : '/static/images/doctor_icon.png'}}" mode="aspectFill"
					class="imgBlock"></image>
			</view>
			<view class="doctorData">
				<view class="" hover-class="none" hover-stop-propagation="false">
					<text class="b c333 f44">{{doctor.name}}</text> <text class="pl25 f28 c666">{{doctor.title}}</text>
				</view>
				<view class="f28 c333 mt5" hover-class="none" hover-stop-propagation="false">
					{{doctor.department}} ｜ {{doctor.hospital}}
				</view>
				<view class="f24 c999 mt5">好评率：<text class="color-primary b mr30">{{doctor.praiseRate}}</text>服务患者次数：<text
						class="color-primary b">{{doctor.serviceNum}}</text></view>
			</view>
			<view class=" f28 c666 pt20 {{textHidden?'textBox':'textShow'}} ">
				<view class="flex">
					<text class="b c333 f28">擅长：</text>
					<view class="flex1 bk">{{doctor.expertise}}</view>
				</view>
				<view class="mt20 flex" hidden="{{textHidden}}">
					<text class="b c333 f28">简介：</text>
					<view class="flex1 bk">{{doctor.introduction}}</view>
				</view>
			</view>
			<view class="donwIcon {{textHidden?'':'upIcon'}}" bindtap="textShowFun">
				<image src="{{imgObject.ic_open}}" class="imgBlock"></image>
			</view>
		</view>
	</view>
	<view class="m20">
		<view class="flex_m">
			<view class="serverItem flex_l_m rel {{serverActive==1?'serverItemActive':''}}" bindtap="serveFun" data-id="1">
				<view class="shadeBox" wx:if='{{sourceType == 1 && sourceType!=3}}'></view>
				<view class="f32 b flex_m ml40">
					<image style="width:40rpx;height:40rpx" src="{{imgObject.ic_doctor_image}}"></image>
					<view class="f32 b c333 ml20 ">
						图文问诊
					</view>
				</view>
				 <view class="serverItem-select" hidden="{{serverActive!=1}}">
            <image src="{{imgObject.ic_doctor_choice}}" class="imgBlock"></image>
          </view>
				<view class="c999 f24 pt10 ml40"><text class="color-danger f32 b">￥{{doctor.consultCost/100}}</text>/次</view>
			</view>
			<view class="serverItem flex_l_m ml20 rel {{serverActive==2?'serverItemActive':''}}" bindtap="serveFun"
				data-id="2">
				<view class="shadeBox" wx:if='{{sourceType == 2 && sourceType!=3}}'></view>
				<view class="f32 b flex_m ml40">
					<image style="width:40rpx;height:40rpx" src="{{imgObject.ic_doctor_video}}"></image>
					<view class="f32 b c333 ml20 ">
						视频问诊
					</view>
				</view>
				 <view class="serverItem-select" hidden="{{serverActive!=2}}">
            <image src="{{imgObject.ic_doctor_choice}}" class="imgBlock"></image>
          </view>
				<view class="c999 f24 pt10 ml40"><text
						class="color-danger f32 b">￥{{doctor.videoCost/100}}</text>/{{doctor.videoLength}}分钟</view>
			</view>
		</view>
	</view>
	<view class="eval bg-color-white m20 p20">
		<view class="f36 b c333 title">
			患者评价（{{doctor.consultCommnetPage.totalCount}}）<view class="fr more n" bindtap="evalMore"
				wx:if="{{doctor.consultCommnetPage.result.length>0}}">查看更多 <image src="{{imgObject.ic_doctor_more}}"></image>
			</view>
		</view>
		<view class="list">
			<block wx:if="{{doctor.consultCommnetPage.result.length>0}}">
				<view class="item pb10" wx:for="{{doctor.consultCommnetPage.result}}" wx:key="index">
					<view class="flex_m">
						<view class="photo">
							<image src="{{item.patientHeadUrl}}" class="imgBlock"></image>
						</view>
						<view class="name flex1">
							<view class=" f32 b c333 lh40">{{item.patientNickName}}</view>
							<view class="c999 f24 pt2 flex_lr_m">
								<view class="" hover-class="none" hover-stop-propagation="false">
									{{item.createdAt}}
								</view>
								<view class="star">
									<block wx:for="{{item.starLevel}}" wx:key="index">
										<image src="{{imgObject.ic_star_bright}}"></image>
									</block>
									<block wx:for="{{5-item.starLevel}}" wx:key="index">
										<image src="{{imgObject.ic_star_grey}}"></image>
									</block>
								</view>
							</view>
						</view>
					</view>
					<view class="p96 pt15 f28 c666">{{item.commentContent}}</view>
					<view class="p96 f24 c999">{{item.diagnosis}}</view>
				</view>
			</block>
			<block wx:else>
				<view class="pt50 pb50 tc f24 c666">该医生暂无评价~</view>
			</block>
		</view>
	</view>
	<view class="fixed b0 l0 w100 bg-color-white pl30 pr30 confir">
		<button bindtap="handleConsult" data-id="{{doctor.doctorId}}" data-type='1' data-departmentCode='{{item.departmentCode}}' wx:if="{{serverActive==1}}"
			disabled="{{sourceType == 1 && sourceType!=3}}">图文问诊 ¥{{doctor.consultCost/100}}/次</button>
		<button bindtap="handleConsult" data-id="{{doctor.doctorId}}" data-name="{{doctor.name}}" data-type='2'
			disabled="{{sourceType == 2 && sourceType!=3}}" wx:else>视频问诊
			¥{{doctor.videoCost/100}}/{{doctor.videoLength}}分钟</button>
	</view>
</view>
<authorization id='authToast' bind:authSub='onAuthSub' bind:close='onClose'></authorization>

<van-overlay show="{{ openView }}" bind:click="onClickHide">
  <view class="wrapper">
    <view class="block">
			<view class="title">
				<image class="bg_authentication" src="{{imgObject.bg_authentication}}" />
				<image class="ic_doctor_authentication" src="{{imgObject.ic_doctor_authentication}}" />
					<view class="tc pt20 b color-primary">
						实名认证信息
					</view>
			</view>
			<view class="wrapper-item m20 p20 flex_lr_m">
				<view class="flex_m">
					<image class="w40 h40" src="{{imgObject.ic_qualification}}" />
						<view class="flex_tb ml20">
							<view class="f28 c333 b">
								医师资格证
							</view>
							<view class="f22 c999">
								资格证编号：{{doctor.certNum}}
							</view>
						</view>
				</view>
				<image class="w35 h35" src="{{imgObject.ic_success}}" />
			</view>
			<view class="wrapper-item m20 p20 flex_lr_m">
				<view class="flex_m">
					<image class="w40 h40" src="{{imgObject.ic_practice}}" />
						<view class="flex_tb ml20">
							<view class="f28 c333 b">
								医师执业证
							</view>
							<view class="f22 c999">
								执业证编号：{{doctor.pracNum}}
							</view>
						</view>
				</view>
				<image class="w35 h35" src="{{imgObject.ic_success}}" />
			</view>
			<view class="wrapper-item m20 p20 flex_lr_m">
				<view class="flex_m">
					<image class="w40 h40" src="{{imgObject.ic_name}}" />
						<view class="flex_tb ml20">
							<view class="f28 c333 b">
								已通过实名认证
							</view>
							<view class="f22 c999">
								保证服务均由认证医生提供
							</view>
						</view>
				</view>
				<image class="w35 h35" src="{{imgObject.ic_success}}" />
			</view>
			<view class="wrapper-button bg-color-primary f28 cfff b m50 flex_c_m" catchtap="onClickHide">
				我已知晓
			</view>
		</view>
  </view>
</van-overlay>
