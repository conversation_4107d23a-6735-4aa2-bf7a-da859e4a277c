// pages/orderTcm/orderTcm.js
const api = require('../../config/api')
const util = require('../../utils/util')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '订单详情',
    titleColor: '#fff',
    imgObject: {
      bg_ordrer: api.ImgUrl + 'images/bg_ordrer.png',
      ic_address: api.ImgUrl + 'images/ic_address.png',
      ic_order_paid: api.ImgUrl + 'images/ic_order_paid.png',
      ic_order_delivered: api.ImgUrl + 'images/ic_order_delivered.png',
      ic_order_receiving: api.ImgUrl + 'images/ic_order_receiving.png',
      ic_order_cancel: api.ImgUrl + 'images/ic_order_cancel.png',
      ic_order_complete: api.ImgUrl + 'images/ic_order_complete.png'
    },
    detail: {},
    templateId: null,
    show: false,
    actions: [
      {
        name: '在线客服',
        openType: 'contact'
      },
      {
        name: '电话客服'
      }
    ],
    platformServiceHotline: ''
  },
  authSub() {
    var that = this
    wx.requestSubscribeMessage({
      tmplIds: that.data.templateId,
      success: (res) => {
        setTimeout(() => {
          that.getDetail()
        }, 1000)
      },
      fail(res) {
        setTimeout(() => {
          that.getDetail()
        }, 1000)
      }
    })
  },
  // 获取模板
  async getTemplate() {
    var templateId = await util.getTemplate(3)
    console.log(templateId, 'templateId')
    this.setData({
      templateId: templateId
    })
  },
  // 支付
  async handlePay(e) {
    util.showLoading({
      title: '请稍后',
      mask: true
    })
    try {
      const { data } = await util.request(util.getRealUrl(api.orderPay, this.data.orderId))
      if (data.code !== 0) {
        throw new Error(data.msg)
      }
      var that = this
      wx.requestPayment({
        ...data.data,
        success: (result) => {
          util.showToast({ title: '支付成功' })
          that.authSub()
        },
        fail: () => {}
      })
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
    util.hideLoading()
  },
  handleCancel() {
    util.showModal({
      title: '',
      content: `取消订单后，再次购买需要咨询医生申请再次开方，确定取消吗？`,
      showCancel: true,
      cancelText: '我点错了',
      cancelColor: '#666666',
      confirmText: '确定',
      success: (result) => {
        if (result.confirm) {
          this.cancelOrder()
        }
      }
    })
  },
  async cancelOrder() {
    try {
      const { data } = await util.request(util.getRealUrl(api.orderCancel, this.data.orderId))
      util.showToast({
        title: data.msg,
        icon: 'none',
        duration: 3000
      })

      if (data.code === 0) {
        setTimeout(() => {
          this.getDetail()
        }, 350)
      }
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
  },
  // 订单详情
  async getDetail() {
    try {
      const { data } = await util.request(util.getRealUrl(api.orderDetail, this.data.orderId))
      if (data.code !== 0) {
        throw new Error(data.msg)
      }
      this.setData({
        detail: data.data
      })
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
  },
  onContact(event) {
    const { serviceHotline, platformServiceHotline } = event.currentTarget.dataset.item
    if (serviceHotline) {
      wx.makePhoneCall({
        phoneNumber: serviceHotline
      })
    } else {
      this.setData({ show: true, platformServiceHotline })
    }
  },
  onSelect(event) {
    if (event.detail.name == '电话客服') {
      wx.makePhoneCall({
        phoneNumber: this.data.platformServiceHotline
      })
    }
  },
  onClose() {
    this.setData({ show: false })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.setData({
      orderId: options.orderId
    })
    console.log(options.orderId, 159)
    this.getDetail()
    this.getTemplate()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {

  }
})
