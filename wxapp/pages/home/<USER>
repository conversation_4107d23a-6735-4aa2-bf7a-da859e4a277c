/* pages/home/<USER>/
.logo {
  width: 500rpx;
  position: absolute;
  left: 20rpx;
  display: flex;
  align-items: center;
}

.logo image {
  height: 40rpx;
}

.bgImg {
  position: fixed;
  left: 0;
  top: 0;
  height: 498rpx;
  width: 100%;
}

.bgImg image {
  display: block;
  width: 100%;
  height: 100%;
}
.search .city-box {
  /* position: absolute;
  left: 20rpx;
  top: 18rpx; */
  padding:0 20rpx;
  border-right: 2rpx solid #dfdfdf;
}
.search .city-title{
  font-size: 28rpx;
  line-height: 28rpx;
}
.city-image{
  width: 18rpx;
  height: 18rpx;
  margin-left: 12rpx;
}
.search .rel {
  border: 2rpx solid #dfdfdf;
  height: 80rpx;
  border-radius: var(--btnRadius);
}

.search .rel input {
  display: block;
  width: 100%;
  height: 100%;
  line-height: 80rpx;
  padding-left: 14rpx;
  box-sizing: border-box;
}
.search .search-box{
  padding-left: 18rpx;
}
.search .icon {
  width: 40rpx;
  height: 40rpx;
  /* position: absolute;
  left: 184rpx;
  top: 19rpx; */
}
.tip-box {
  padding: 0 20rpx;
}
.tip-box .imgDun {
  width: 24rpx;
  height: 28rpx;
  margin-right: 8rpx;
}
.menuItem {
  width: 344rpx;
  height: 160rpx;
}

.banner {
  margin: 0 auto;
  width: 710rpx;
  height: 240rpx;
}
swiper {
  width: 710rpx;
  height: 240rpx;
}
swiper-item{
  border-radius: 8rpx;
}

.dots {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 20rpx;
  display: flex;
  justify-content: center;
}

.dots .dot {
  margin: 0 8rpx;
  width: 10rpx;
  height: 10rpx;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  transition: all 0.6s;
}

.dots .dot.active {
  background: #2e9cff;
  width: 20rpx;
  height: 10rpx;
  border-radius: 6rpx;
  background: #ffffff;
}

.auth-content {
  width: 614rpx;
  border-radius: 20rpx;
  background-color: #fff;
  z-index: 99999;
  padding: 30rpx 0;
}

.container .van-popup {
  border-radius: 10px;
  border-radius: var(--popup-round-border-radius, 10px);
  position: fixed;
  box-sizing: border-box;
  max-height: 100%;
  overflow-y: auto;
  transition-timing-function: ease;
  -webkit-animation: ease both;
  animation: ease both;
  -webkit-overflow-scrolling: touch;
  background-color: #fff;
  background-color: var(--popup-background-color, #fff);
}

.auth-btn {
  background: var(--themeColor);
  margin: 10rpx 20rpx 0 20rpx;
  height: 80rpx;
  border-radius: 8rpx;
}

.login {
  margin: 0 auto;
  width: 272rpx;
  height: 84rpx;
  text-align: center;
  line-height: 84rpx;
  background: var(--themeColor);
  color: #fff;
  margin-bottom: 40rpx;
  border-radius: var(--btnRadius);
}

.noData image {
  display: block;
  width: 380rpx;
  height: 316rpx;
  margin: 0 auto;
  margin-top: 50rpx;
}
.bl1 {
  height: 20rpx;
  border-left: 1rpx solid #dddddd;
}

.navActive {
  color: #333;
  font-weight: bold;
}
.navActive image{
  width: 36rpx;
  height: 12rpx;
  position: absolute;
  bottom: -12rpx;
}

.fixedLogo {
  width: 500rpx;
  display: flex;
  align-items: center;
}
.fixedLogo image {
  width: 500rpx;
  height: 40rpx;
  margin-left: 20rpx;
}
.bg-color-gray {
  background: #fdfdfd;
}

.dep_item{
  width: 210rpx;
  height: 80rpx;
  background: #F3F7FF;
  border-radius: 8rpx;
}

.more-text {
  color: #8B8B8B;
  font-size: 24rpx;
}

.fl_align {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mt28 {
  margin-top: 28rpx;
}

.dis_container {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  height: 140rpx;
  overflow: hidden;
}

.item {
  font-size: 28rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #4285FF;
  padding: 8rpx 16rpx;
  background: #FFFFFF;
  border-radius: 4rpx;
  border: 1rpx solid #367DFF;
  max-width: 200rpx;
  height: fit-content;
  white-space: nowrap; /* 禁止换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
}

.mt1 {
  margin-top: 2rpx;
}

.mr20 {
  margin-right: 20rpx;
}
