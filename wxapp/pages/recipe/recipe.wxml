<!-- 个人中心 我的处方进入 -->
<navbar  isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
<view wx:if="{{type != '1'}}">
  <view class="w100 fixed zx999 flex tab" style="top:{{ 44 + statusBarHeight}}px;">
    <view wx:for="{{status}}" class="flex1 flex_c_m" wx:key="index">
         <text class=" {{listQuery.status==item.id?'cur':''}}" bindtap="tabClick" data-id="{{item.id}}">{{item.name}}</text>
    </view>

  </view>
  <view style="padding-top:84rpx">
    <template is="list" data="{{presList, type,static}}"/>
  </view>
</view>
<!-- 首页复诊开药进入 -->
<view wx:else>
  <template is="list" data="{{presList, type,static}}"/>
</view>

<template name="list">
  <view class="List">
    <view class="item m30" wx:for="{{presList}}" wx:key="index">
      <view data-drugType='{{item.drugType}}' data-recomId='{{item.recomId}}' bind:tap="goDetail">
        <view class="item-box bg-color-white pl30 pr30">
          <!-- <navigator class="pt30 c333" url='/pages/drugClock/index?recomId={{item.recomId}}'>设置提醒</navigator> -->
          <view class="pt30 pb30 bb1 f32 c333">
            <text>{{item.patientName}}</text>
            <text class="fr b c38BF87" wx:if="{{item.status==1 || item.status==2 || item.status==4}}">已购买</text>
            <text class="fr b c999" wx:elif="{{item.expire==1 }}">已失效</text>
            <text class="fr b cF06454" wx:elif="{{item.status==0 }}">待购买</text>
          </view>
          <view class="pt30 pb30 bb1">
            <view class="c333 f28 b">诊断：{{item.diagnosis}}</view>
            <view class="h2 f28 c333">RP：{{item.drugNames}}</view>
          </view>
          <view class="pt30 pb30 c999 f24 clearfix">
            <block wx:if="{{type != '1'}}">
              <text>医生：{{item.doctorName}}</text>
              <text class="fr">{{item.recomTime}}</text>
            </block>
            <block wx:else>
              <text style="line-height:60rpx">{{item.recomTime}}</text>
              <view class="btn b" data-id="{{item.recomId}}" data-name="{{item.doctorName}}" catchtap="applyFor">立即申请</view>
            </block>
          </view>

        </view>
      </view>
    </view>

    <view wx:if="{{!presList.length}}" class="flex_line_c no_msg_box">
      <image class="no_msg" src="{{static.nomes}}"></image>
      <view class="f28 c666">暂无数据</view>
    </view>
  </view>
</template>
