// pages/consult/order/detail/index.js
const util = require('../../../../utils/util')
const api = require('../../../../config/api.js')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    orderSn: '',
    static: {
      bg_video_interrogation: api.ImgUrl + 'images/bg_video_interrogation_01.png',
      ic_cancel_01: api.ImgUrl + 'images/ic_cancel_01.png',
      ic_completed_01: api.ImgUrl + 'images/ic_completed_01.png',
      ic_in_progress: api.ImgUrl + 'images/ic_in_progress.png',
      ic_refund: api.ImgUrl + 'images/ic_refund.png',
      ic_waiting_02: api.ImgUrl + 'images/ic_waiting_02.png'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      orderSn: options.orderSn
    }, () => {
      this.getData()
    })
  },
  async getData() {
    const { orderSn } = this.data
    try {
      const { data } = await util.request(`${api.consultOrderDetail}?orderSn=${orderSn}`)
      if (data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
        return
      }
      console.log(data, 'data')
      data.data.offlineDiagnosisImg = data.data.offlineDiagnosisImg.split(',')
      this.setData({
        detail: data.data
      })
      console.log(this.data.detail)
    } catch (error) {
      throw new Error(error)
    }
  },
  handleConsult(e) {
    const { doctorid, status, type,id,roomid } = e.currentTarget.dataset
    if (status === 3 || status === 4 || status === 5) {
      wx.navigateTo({
        url: `/pages/famousDoctorDetail/famousDoctorDetail?type=3&doctorId=${doctorid}`
      })
      return
    }
    if (type === 1) {
        wx.navigateTo({
          url: `/pages/consult/chat/chat?doctorId=${doctorid}`
        })
    }else{
      if(status === 1){
        util.showToast({
          title: '医生未接诊',
          icon: 'none',
          duration: 3000
        })
      }else{
        wx.navigateTo({
          url: `/pages/meeting/meeting?roomID=${roomid}&videoConsultId=${id}`
        })
      }
    }
  },
  handlePreview: function(e) {
    const { detail } = this.data
    var { index } = e.currentTarget.dataset
    wx.previewImage({
      current: detail.offlineDiagnosisImg[index],
      urls: detail.offlineDiagnosisImg
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})
