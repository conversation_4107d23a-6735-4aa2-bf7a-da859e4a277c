<navbar
  isBack="{{ true }}"
  backgroundColor="#367DFF"
  isWhite="{{ true }}"
></navbar>
<view class="container rel">
  <image class="bg_header" src="{{ static.bg_video_interrogation }}" />
  <view class="content">
    <view class="flex_c_m cfff b f36" wx:if="{{ detail.consultStatus == 1 }}"
      >　 <image class="w40 h40 mr10" src="{{ static.ic_waiting_02 }}" />待接诊
    </view>
    <view class="flex_c_m cfff b f36" wx:if="{{ detail.consultStatus == 2 }}">
      <image class="w40 h40 mr10" src="{{ static.ic_in_progress }}" />进行中
    </view>
    <view class="flex_c_m cfff b f36" wx:if="{{ detail.consultStatus == 3 }}">
      <image class="w40 h40 mr10" src="{{ static.ic_completed_01 }}" />已完成
    </view>
    <view class="flex_c_m cfff b f36" wx:if="{{ detail.consultStatus == 4 }}">
      <image class="w40 h40 mr10" src="{{ static.ic_cancel_01 }}" />已取消
    </view>
    <view class="flex_c_m cfff b f36" wx:if="{{ detail.consultStatus == 5 }}">
      <image class="w40 h40 mr10" src="{{ static.ic_refund }}" />已退款
    </view>

    <view class="f22 tc mt10" style="color: #bed5ff" wx:if="{{detail.refusalReason}}"> 
      原因：{{detail.refusalReason}}
    </view>
    <view class="order_info m20 p20 bg-color-white container-radius">
      <view class="f32 c333 b"> 订单信息 </view>
      <view class="f28 mt15 c666">
        问诊费：<text class="color-danger b">¥{{ detail.price / 100 }}</text>
      </view>
      <view class="f28 mt10 c666">
        问诊类型：{{ detail.consultType == 1 ? "图文咨询" : "视频咨询" }}
      </view>
      <view class="f28 mt10 c666"> 订单编号：{{ detail.orderSn }} </view>
      <view class="f28 mt10 c666"> 下单时间：{{ detail.createdAt }} </view>
      <view class="f28 mt10 c666" wx:if="{{detail.completeTime}}"> 结束时间：{{ detail.completeTime || '' }} </view>
      <view class="f28 mt10 c666" wx:if="{{ detail.consultStatus == 5 }}"> 退款时间：{{ detail.refundTime }} </view>
    </view>
    <view class="m20 p20 bg-color-white container-radius">
      <view class="f32 c333 b"> 医生信息 </view>
      <view class="f28 mt15 c666 flex_m">
        <image
          class="doctor_avatar"
          src="{{ detail.headUrl }}"
        />
        <view class="flex_l_m flex1 ml20">
          <view>
            <text class="f36 c333 b">{{ detail.doctorName }}</text
            ><text class="f28 c333 ml10">{{detail.title}}</text>
          </view>
          <view>
            <text class="f28 c666">{{ detail.departmentName }}</text
            ><text class="f28 c666 ml10">{{detail.hospitalName}}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="m20 p20 bg-color-white container-radius">
      <view class="f32 c333 b"> 病情信息 </view>
      <view class="f28 mt15 c666">
        就诊人：{{ detail.inquirerName }}
        {{ detail.inquirerGender == 1 ? "男" : "女" }} {{ detail.inquirerAge }}
      </view>
      <view class="f28 mt10 c666"> 是否线下就诊：是 </view>
      <view class="f28 mt10 c666">
        线下诊断：{{ detail.offlineDiagnosis }}</view
      >
      <!-- <view class="f28 mt10 c666">
        病情描述：{{detail.conditionDesc || ''}}
      </view> -->
      <view class="f28 mt10 c666"> 既往史：{{detail.pastHistory ? detail.pastHistory : '无'}} </view>
      <view class="f28 mt10 c666"> 过敏史：{{detail.allergy ? detail.allergy : '无'}}</view>
    </view>
    <view class="m20 p20 bg-color-white container-radius">
      <view class="f32 c333 b"> 图片资料 </view>
      <view class="img_list mt15">
        <image
          class="img_item"
          wx:for="{{ detail.offlineDiagnosisImg }}"
          wx:for-item="k"
          wx:key="index"
          src="{{ k }}"
          data-index="{{ index }}"
          bindtap="handlePreview"
        ></image>
      </view>
    </view>
  </view>
</view>
<view class="fixed b0 l0 w100 bg-color-white">
  <view class="confir pl30 pr30">
    <button
      data-status="{{ detail.consultStatus }}"
      data-type="{{ detail.consultType }}"
      data-doctorId="{{ detail.doctorId }}"
      data-id="{{ detail.id }}"
      data-roomid="{{ detail.roomId }}"
      catch:tap="handleConsult"
    >
      {{
        detail.consultStatus == 3 ||
        detail.consultStatus == 4 ||
        detail.consultStatus == 5
          ? "再次咨询"
          : "继续咨询"
      }}
    </button>
  </view>
</view>
