<template name="consult">
	<!-- 消息记录 -->
	<scroll-view enable-flex='true' scroll-y scroll-anchoring="{{true}}" class='record-wrapper'
		scroll-into-view="{{scrollIntoView}}" id="recordWrapper" enable-passive='{{true}}' upper-threshold="100"
		style="bottom: {{isAgain ? '' : 'unset'}};height: {{scrollHeight}};top:{{scrollTop}}" bindscrolltoupper="loadMore">
		<block wx:for="{{messageArr}}" wx:for-item="item" wx:key="{{item.sendTimestamp}}">
			<view class="timeText" hover-class="none" wx:if='{{message.timeGroup}}' hover-stop-propagation="false">
				{{message.timeText}}
			</view>
			<view  id="chat_{{item.sendTimestamp}}" wx:for-item="item"
				hover-class="none" hover-stop-propagation="false">
				<view wx:if="{{item.sender == 0}}" class='{{item.sender == 0 ? "record-chatting-item  other" : ""}}' style='justify-content: flex-start'>
					<view class="right-triangle mr20">
					<image class="avatar" src="{{item.counselorAvatar ? item.counselorAvatar : '/static/images/counselor_default_avatar.png'}}" mode="aspectFit" />
				</view>
				<view class="record-chatting-item-left flex1">
					<view class="f24 c999">
						{{item.timeText}}
					</view>
					<view wx:if="{{item.sendType == 0}}" class='record-chatting-item-text sendtext f32'>{{item.sendContent}}
					</view>
					<view wx:if="{{item.sendType == 1}}" class='record-chatting-item-image sendimage'>
						<image bindtap="previewImage" data-src="{{item.sendContent}}" mode='aspectFill'
							src="{{item.sendContent}}"></image>
					</view>
					<view wx:if="{{item.sendType == 2}}" class="record-chatting-item-ques" bindtap="handleQues" data-item="{{item}}">
						<image class="status" src="/static/images/<EMAIL>" wx:if="{{item.fillingStatus}}"></image>
						<view class="head">
							<image src="/static/images/<EMAIL>"></image>
							<view class="text">{{item.title}}</view>
						</view>
						<view class="cont">
							<view class="title">{{item.formName}}</view>
							<view class="desc">{{item.sendContent}}</view>
						</view>
						<view class="foot">
							查看详情
						</view>
					</view>
				</view>
				</view>
				<view
					wx:if="{{item.sender == 1 && item.sendType !== 3}}"
					class='{{item.sender == 1 ? "record-chatting-item self" : ""}}' style='justify-content: flex-end'>
					<view class="record-chatting-item-right flex1">
						<view class="f24 c999">
							{{item.timeText}}
						</view>
						<view wx:if="{{item.sendType == 0}}" class='record-chatting-item-text receivetext f32'>
							{{item.sendContent}}
						</view>
						<view wx:if="{{item.sendType == 1}}" class='record-chatting-item-image receiveimage'>
								<image bindtap="previewImage" data-src="{{item.sendContent}}" mode='aspectFill'
									src="{{item.sendContent}}"></image>
						</view>
					</view>
						<image catchtap='goDoctorDetail' mode="aspectFill" src="{{item.patientAvatar ? item.patientAvatar : '/static/images/default_avatar.png'}}"
						class='record-chatting-item-img ml20'></image>
				</view>
			</view>
		</block>
	</scroll-view>
	<!-- <view class="flex_line_c no_msg_box" wx:if='{{!messageArr.length}}'>
		<image class="no_msg" src="{{static.nomes}}"></image>
		<view class="f28 c666">暂无问诊</view>
	</view> -->
</template>
