<!--pages/consult/index/index.wxml-->
<import src='../template/videoConsult.wxml' />
<view class="container">
  <view wx:if="{{showTabBar}}" class="tab flex">
    <view wx:if="{{hasCounselor}}" class="flex1 flex_c_m">
      <text class="{{type==0?'cur':''}}" bindtap="tabClick" data-id="0">诊前咨询</text>
    </view>
     <view class="flex1 flex_c_m {{(!hasCounselor && !videoSwitch) ? 'flex3' : (!hasCounselor || !videoSwitch) ? 'flex2' : ''}}">
        <text class="{{type==1?'cur':''}}" bindtap="tabClick" data-id="1">图文咨询<text wx:if="{{hasGraphicMessage}}" class="dot"></text></text>
     </view>
     <view wx:if="{{videoSwitch}}" class="flex1 flex_c_m {{(!hasCounselor && !videoSwitch) ? 'flex3' : (!hasCounselor || !videoSwitch) ? 'flex2' : ''}}">
        <text class="{{type==2?'cur':''}}"  bindtap="tabClick" data-id="2">视频咨询<text wx:if="{{hasVideoMessage}}" class="dot"></text></text>
     </view>
  </view>
  <!-- 诊前咨询 -->
  <view class="list {{showTabBar ? 'pt85' : 'pt30'}}" wx:if="{{type==0}}">
    <view class="pl30 pr30 bg-color-white" wx:if='{{counselorList.length>0}}' >
     <view class="item pt30 pb30  clearfix bb1" wx:for="{{counselorList}}" wx:key="{{index}}" data-id='{{item.counselorId}}' data-preConsultId='{{item.id}}' bindtap='getCounselorDetail'>
        <view class="photo fl">
          <image mode="aspectFill" src="{{item.avatar ? item.avatar : '/static/images/counselor_default_avatar.png'}}" class="imgBlock"></image>
          <text wx:if='{{messageRecord[item.doctorId]}}'>{{messageRecord[item.doctorId]}}</text>
          <view class="ms_tag" wx:if='{{!item.patientRead}}'></view>
        </view>
        <view>
          <view class="title">
            <text class="c333 b f32">{{item.name}}</text>
            <text class="c666 f26 pl10">{{item.title}}</text>
            <text class="c999 f24 fr" wx:if='{{item.lastReplyTime}}'>{{item.lastReplyTime}}</text>
          </view>
          <view class="f28 pt10 pl20 c999 lh40 ell" wx:if='{{item.lastReplyContent }}'>{{item.lastReplyContent}}</view>
        </view>
     </view>
    </view>
		 <view class="flex_line_c no_msg_box" wx:else>
			<image class="no_msg" src="{{static.nomes}}"></image>
			<view class="f28 c666">暂无咨询</view>
		</view>
  </view>
  <!-- 图文问诊 -->
  <view class="list {{showTabBar ? 'pt85' : 'pt30'}}" wx:if="{{type==1}}">
    <view class="pl30 pr30 bg-color-white" wx:if='{{list.length>0}}' >
     <view class="item pt30 pb30  clearfix bb1" wx:for="{{list}}" wx:key="{{index}}" data-id='{{item.doctorId}}' bindtap='getMsgDetail'>
        <view class="photo fl">
          <image mode="aspectFill" src="{{item.photo ? item.photo : '/static/images/doctor_icon.png'}}" class="imgBlock"></image>
          <text wx:if='{{messageRecord[item.doctorId]}}'>{{messageRecord[item.doctorId]}}</text>
        </view>
        <view>
          <view class="title">
            <text class="c333 b f32">{{item.doctorName}}</text>
            <text class="c666 f26 pl10">{{item.title}}</text>
            <image src="{{icon}}" wx:if="{{item.aliveMessage}}"></image>
            <text class="c999 f24 fr">{{item.consultTime}}</text>
          </view>
          <view class="content f28 c999 lh40">{{item.messageContent}}</view>
        </view>
     </view>
    </view>
		 <view class="flex_line_c no_msg_box" wx:else>
			<image class="no_msg" src="{{static.nomes}}"></image>
			<view class="f28 c666">暂无问诊</view>
		</view>
  </view>
  <!-- 视频问诊 -->
  <view class="{{showTabBar ? 'pt85' : 'pt30'}} bg-color-white" wx:if="{{type==2}}">
    <template is='videoConsult' data="{{videoConsult,static}}"></template>
  </view>
</view>
