// pages/consult/index/index.js
const api = require('../../../config/api')
const util = require('../../../utils/util')
const Config = require('../../../config/index.js')
const app = getApp()

Page({

  /**
	 * 页面的初始数据
	 */
  data: {
    ...app.globalData.userInfo,
    type: 0,
    icon: api.ImgUrl + 'images/ic_consult.png',
    listQuery: {
      page: 1 // 页码
    },
    list: [],
    videoConsult: [],
    counselorList: [],
    static: {
      nomes: api.ImgUrl + 'images/nomes.png'
    },
    start:'',
    hasGraphicMessage: false,
    hasVideoMessage: false,
    hasCounselor: true,
    videoSwitch: false // 视频开关状态，默认关闭
  },
  onMessageArrived() {
    this.setData({
      messageRecord: util.getChatData('messageRecord')
    })
    this.data.listQuery.page = 1
    if (this.data.type == 0) {
      this.getCounselorList(1)
    }
    if (this.data.type == 1) {
      this.getConsultList(1)
    }
    if (this.data.type == 2) {
      this.getConsultVideoList(1)
    }
    // this.data.type === 1 ? this.getConsultList(1) : this.getConsultVideoList(1)
    if (this.data.type === 2 && arguments[0].c === 1 && arguments[0].t === 12) {
      this.getConsultVideoList(1)
    }
  },
  tabClick(e) {
    var id = e.currentTarget.dataset.id
    
    // 检查功能是否可用
    if (id == 0 && !this.data.hasCounselor) {
      console.log('咨询师功能未开启')
      return
    }
    if (id == 2 && !this.data.videoSwitch) {
      console.log('视频功能未开启')
      return
    }
    
    this.data.listQuery.page = 1
    this.data.loadComplete = true
    this.data.list = []
    this.data.videoConsult = []
    this.data.counselorList = []
    const messageRecord = util.getChatData('messageRecord') || {}
    this.setData({
      type: id
    }, () => {
      // 根据选择的tab和功能开关加载对应数据
      if (id == 0 && this.data.hasCounselor) {
        this.getCounselorList(1)
      }
      if (id == 1) {
        this.getConsultList(1)
      }
      if (id == 2 && this.data.videoSwitch) {
        this.getConsultVideoList(1)
      }
      if (id === '2') {
        delete messageRecord['video']
        wx.setStorageSync('messageRecord', messageRecord)
        app.getMessageNum()
        this.setData({
          hasVideoMessage: false
        })
      }
    })

  },
  getConsultList(type) {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    this.data.listQuery.patientId = app.globalData.userInfo.userId
    util.request(api.consultText, this.data.listQuery, 'GET')
      .then(res => {
        util.hideToast()
        if (res.data.code === 0) {
          const result = res.data.data
          const messageRecord = util.getChatData('messageRecord') || {}
          result.result.forEach(element => {
            element.consultTime = util.calcTimeHeader(element.consultTimestamp)
            if (Object.keys(messageRecord).includes(element.doctorId.toString())) {
              this.setData({
                hasGraphicMessage: true
              })
            }
          })
          console.log(result, 89)
          ++this.data.listQuery.page
          this.setData({
            list: type === 1 ? result.result : this.data.list.concat(result.result),
            listQuery: this.data.listQuery,
            loadComplete: !result.hasNext ? false : true
          })
        } else {
          util.showToast({
            icon: 'none',
            title: res.data.msg
          })
        }
      })
      .catch(res => {
        console.log(res)
      })
  },
  getConsultVideoList(type) {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    this.data.listQuery.patientId = app.globalData.userInfo.userId
    util.request(api.consultVideo, this.data.listQuery, 'GET')
      .then(res => {
        util.hideToast()
        const result = res.data.data
        ++this.data.listQuery.page
        if (res.data.code === 0) {
          this.setData({
            videoConsult: type === 1 ? result.result : this.data.videoConsult.concat(result.result),
            listQuery: this.data.listQuery,
            loadComplete: !result.hasNext ? false : true
          })
        } else {
          util.showToast({
            icon: 'none',
            title: res.data.msg
          })
        }
      })
      .catch(res => {
        console.log(res)
      })
  },
  getCounselorList(type) {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    const params = {
      patientId: app.globalData.userInfo.userId
    }
    util.request(api.counselorConsult, params, 'GET')
      .then(res => {
        console.log(res)
        util.hideToast()
        const result = res.data
        ++this.data.listQuery.page
        if (res.statusCode === 404) {
          this.setData({
            type: 1,
            hasCounselor: false
          })
          return
        }
        if (res.data.code === 0) {
          this.setData({
            counselorList: type === 1 ? result.data : this.data.counselorList.concat(result.data),
            listQuery: this.data.listQuery,
            loadComplete: !result.hasNext ? false : true
          })
        } else {
          util.showToast({
            icon: 'none',
            title: res.data.msg
          })
        }
      })
      .catch(res => {
        console.log(res)
      })
  },
  /**
	 * 生命周期函数--监听页面加载
	 */
  async onLoad(options) {
    this.setData({
      start:options.start || ''
    })
  },
  onReachBottom: function() {
    if (this.data.loadComplete) {
      ++this.data.listQuery.page
      // this.data.type === 1 ? this.getConsultList(2) : this.getConsultVideoList(2)
      if (this.data.type == 0 && Config.features.showCounselor) {
        this.getCounselorList(2)
      }
      if (this.data.type == 1) {
        this.getConsultList(2)
      }
      if (this.data.type == 2) {
        this.getConsultVideoList(2)
      }
    }
  },
  getMsgDetail(e) {
    const id = e.currentTarget.dataset.id
    const messageRecord = util.getChatData('messageRecord') ? util.getChatData('messageRecord') : {}
    delete messageRecord[id]
    if (this.data.hasGraphicMessage) {
      this.setData({
        hasGraphicMessage: false
      })
    }
    wx.setStorageSync('messageRecord', messageRecord)
    this.setData({
      messageRecord
    }, () => {
      wx.navigateTo({
        url: '/pages/consult/chat/chat?doctorId=' + id
      })
    })
    app.getMessageNum()
  },
  getCounselorDetail(e) {
    const { id, preconsultid } = e.currentTarget.dataset
    wx.navigateTo({
      url: '/pages/consult/counselor/index?id=' + id + '&preConsultId=' + preconsultid
    })
  },
  goMeeting(e) {
    const videoconsultstatus = e.currentTarget.dataset.videoconsultstatus
    const roomID = e.currentTarget.dataset.roomid
    const videoConsultId = e.currentTarget.dataset.videoconsultid
    if (videoConsultId != null) {
      switch (videoconsultstatus) {
        case 1:
          this.getAppIdAndKey(videoConsultId, roomID)
          break
        case 2:
          this.getAppIdAndKey(videoConsultId, roomID)
          break
        case 3:
          this.getAppIdAndKey(videoConsultId, roomID)
          break
        case 4:
          util.showToast({ title: '当前问诊已取消' })
          break
        case 5:
          util.showToast({ title: '当前问诊已完成' })
          break
        case 6:
          util.showToast({ title: '当前问诊未付款' })
          break
      }
    }
  },
  getAppIdAndKey(videoConsultId, roomID) {
    util.request(api.getAppIdAndKey, {
      videoConsultId: videoConsultId
    })
      .then(res => {
        util.hideToast()
        if (res.data.code === 0) {
          wx.navigateTo({
            url: `/pages/meeting/meeting?roomID=${roomID}&videoConsultId=${videoConsultId}`
          })
          const messageRecord = util.getChatData('messageRecord') || {}
          delete messageRecord['video']
          wx.setStorageSync('messageRecord', messageRecord)
          app.getMessageNum()
        } else {
          util.showToast({
            icon: 'none',
            title: res.data.msg
          })
        }
      })
      .catch(err => {
        console.log(err)
      })
    // })
  },

  /**
	 * 生命周期函数--监听页面初次渲染完成
	 */
  onReady: function() {

  },

  /**
	 * 生命周期函数--监听页面显示
	 */
  async onShow() {
    var that = this
    var type = app.globalData.consultType * 1
    const messageRecord = util.getChatData('messageRecord') || {}
    var doctorName = app.globalData.doctorName
    const {start} = that.data
    if (type === 2 && doctorName !== '' && !start) {
      wx.showModal({
        content: '您的视频申请已发送给' + doctorName + '医生，请您耐心等待医生接诊~',
        showCancel: false,
        confirmText: '知道了',
        success: (result) => {
          if (result.confirm) {
            that.setData({
              ['listQuery.page']: 1
            })
          }
        }
      })
    }
    
    // 获取全局视频开关状态
    const videoSwitch = app.globalData.videoSwitch
    const hasCounselor = this.data.hasCounselor
    
    // 智能选择可用的tab
    if (!hasCounselor && type === 0) {
      // 如果咨询师功能关闭且当前在咨询师tab，切换到图文咨询
      type = 1
    }
    if (!videoSwitch && type === 2) {
      // 如果视频开关关闭且当前在视频咨询tab，切换到图文咨询
      type = 1
    }
    
    this.setData({
      type: type,
      list: [],
      videoConsult: [],
      ['listQuery.page']: 1,
      hasVideoMessage: Object.keys(messageRecord).includes('video') ? true : false,
      videoSwitch: videoSwitch
    }, async() => {
      const userInfo = wx.getStorageSync('userInfo')
      if (userInfo.token) {
        // 根据功能开关决定是否加载对应数据
        if (hasCounselor) {
          this.getCounselorList(1)
        }
        this.getConsultList(1)
        if (videoSwitch) {
          this.getConsultVideoList(1)
        }
      }
    })
    this.setData({
      messageRecord: util.getChatData('messageRecord')
    })
    app.getMessageNum()

  },

  /**
	 * 生命周期函数--监听页面隐藏
	 */
  onHide: function() {
    app.globalData.consultType = this.data.type
    app.globalData.doctorName = ''
    this.flag = false
  },

  /**
	 * 生命周期函数--监听页面卸载
	 */
  onUnload: function() {

  },

  /**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
  onPullDownRefresh: function() {
    this.data.listQuery.page = 1
    this.data.loadComplete = true
    this.data.list = []
    this.data.videoConsult = []
    this.data.counselorList = []
    if (this.data.type == 0 && this.data.hasCounselor) {
      this.getCounselorList(1)
    }
    if (this.data.type == 1) {
      this.getConsultList(1)
    }
    if (this.data.type == 2 && this.data.videoSwitch) {
      this.getConsultVideoList(1)
    }
    setTimeout(function() {
      wx.stopPullDownRefresh()
    }, 1000)
  }

  /**
	 * 用户点击右上角分享
	 */
  // onShareAppMessage: function() {

  // }
})
