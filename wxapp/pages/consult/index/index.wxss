/* pages/consult/index/index.wxss */
page{
  background: #fff;
}
.tab{
  width: 100%;
  height: 84rpx;
  background: #fff;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.flex2 {
  flex: 2;
}
.tab view{
  /* width: 50%; */
  text-align: center;
  font-size: 28rpx;
  color: #666666;
  height: 84rpx;
  line-height: 84rpx;
}
.tab view text{
  display: inline-block;
  height: 100%;
  position: relative;
}
.tab view text.cur{
  color: var(--themeColor);
  font-weight: bold;
}
.tab view text.cur::after{
  position: absolute;
  content: '';
  width: 40rpx;
  height: 8rpx;
  border-radius: 2rpx;
  background: var(--themeColor);
  left: 50%;
  margin-left: -20rpx;
  bottom: 0rpx;
}
.list .item .photo{
  width: 100rpx;
  height: 100rpx;
  float: left;
  position: relative;
}
.list .item .photo image{
  border-radius: 50%;
}
.list .item .photo text{
  position: absolute;
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 10rpx;
  background: var(--redColor);
  color: #fff;
  line-height: 32rpx;
  font-size: 22rpx;
  font-weight: bold;
  border-radius: 50%;
  top: -6rpx;
  right: -6rpx;
}
.list .item .title{
  padding-left: 120rpx;
  padding-top: 2rpx;
  line-height: 44rpx;
  height: 44rpx;
}
.list .item .title text{
  display: inline-block;
  height: 100%;
  vertical-align: top;
}
.list .item .content{
  padding-left: 120rpx;
  padding-top: 12rpx;
}
.list .item .title image{
  width: 32rpx;
  height: 32rpx;
  vertical-align: top;
  margin-left: 20rpx;
  margin-top: 6rpx;
}
.list .item .content .tag{
  float: right;
  width: 96rpx;
  height: 36rpx;
  text-align: center;
  line-height: 36rpx;
  font-size: 20rpx;
  color: #fff;
  border-radius: 4rpx;
}
.tag1{
  background: #2893ff;
}
.tag2{
  background: #CCCCCC;
}
.tag3{
  background:#38bf87;
}
.tag4{
  background: #FC7D70;
}
.tag5{
  background: #FF9B3A;
}
.cause{
  width: 100%;
  padding: 20rpx;
  font-size: 24rpx;
  color: var(--redColor);
  background: #F8F8F8;
  border-radius: 8rpx;
  margin-top: 20rpx;
}
.pt85 {
  padding-top: 85rpx;
}
.no_msg_box {
  width: 100%;
  position: absolute;
  top: 150rpx;
}
.dot {
  vertical-align:text-top;
  display:inline-block;
  margin-left:4rpx;
  background-color:red;
  border-radius:50%;
  width:16rpx;
  height:16rpx!important;
}