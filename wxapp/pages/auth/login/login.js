var api = require('../../../config/api.js')
var util = require('../../../utils/util.js')
var Config = require('../../../config/index.js')
var app = getApp()
Page({
  data: {
    userInfo: {},
    canIUseGetUserProfile: false,
    company: Config.company,
    login_logo: Config.login_logo,
    isBack: false,
    backgroundColor: '#fff',
    navTitle: '登录',
    paramsObj: null
  },
  onLoad: function(options) {
    let params = options && options.params
    // 解码并反序列化 JSON 字符串为对象
    if (params) {
      let paramsObj = JSON.parse(decodeURIComponent(params))
      this.setData({ paramsObj }) 
    }
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }
  },
  onReady: function() {

  },
  onShow: function() {

  },
  onHide: function() {
    // 页面隐藏

  },
  onUnload: function() {
    // 页面关闭

  },
  getUserProfile(e) {
    // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认
    // 开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
    wx.getUserProfile({
      desc: '授权微信信息', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (res) => {
        this.setData({
          userInfo: res
        }, () => {
          this.checkLoginFunc()
        })
        // 获取信息成功后
      }
    })
  },
  wxLogin: function(e) {
    if (e.detail.userInfo == undefined) {
      app.globalData.hasLogin = false
      util.showToast({ title: '登录失败' })
      return
    }
    this.setData({
      userInfo: e.detail
    }, () => {
      this.checkLoginFunc()
    })
  },
  checkLoginFunc() {
    util.checkLogin().then(() => {
      this.startRegister(util.getUserInfo())
    }).catch(() => {
      util.loginByWeixin().then(res => {
        if (res.data.code == 0 && res.data.data.loginStatus == 1) {
          this.startRegister(util.getUserInfo())
        } else {
          util.showToast({ title: '登录成功' })
          app.globalData.hasLogin = true
          this.getUserInfo()
          const pages = getCurrentPages()
          const prevPage = pages[pages.length - 2] // 获取上一个页面实例对象
          const options = wx.getStorageSync('e') ? wx.getStorageSync('e') : null
          const orderId = wx.getStorageSync('orderId') ? wx.getStorageSync('orderId') : ''
          wx.navigateBack({
            delta: 1,
            success: function () {
              if(options) {
                prevPage.setData({
                  counselorId: options.id,
                  patientId: app.globalData.userInfo.userId,
                  avatar: app.globalData.userInfo.avatar//用户头像
                })
                prevPage.initCounselor(options)
              }
              // 返回代付详情
              if(orderId) {
                prevPage.setData({
                  orderId: orderId,
                })
                prevPage.getDetail(orderId)
              }
              // 刷新上一个页面数据
              if (this.data.paramsObj && prevPage) {
                prevPage.onLoad(this.data.paramsObj)
              }
            }
          })
        }
      }).catch((err) => {
        app.globalData.hasLogin = false
        util.showToast({ title: '登录失败' })
      })

    })
  },
  startRegister: function(userInfo) {
    // 允许授权获取用户信息
    this.data.userInfo.openId = userInfo.openId
    this.data.userInfo.unionId = userInfo.unionId
    this.requestRegister(this.data.userInfo)
  },
  requestRegister: function(params) {
    const that = this
    util.registerUser(params).then(res => {
      if (res.data.code == 0) {
        util.showToast({ title: '登录成功' })
        const pages = getCurrentPages()
        const prevPage = pages[pages.length - 2] // 获取上一个页面实例对象
        const options = wx.getStorageSync('e') ? wx.getStorageSync('e') : null
        const orderId = wx.getStorageSync('orderId') ? wx.getStorageSync('orderId') : ''
        wx.navigateBack({
          delta: 1,
          success: function() {
            if(options) {
              prevPage.setData({
                counselorId: options.id,
                patientId: app.globalData.userInfo.userId,
                avatar: app.globalData.userInfo.avatar//用户头像
              })
              prevPage.initCounselor(options)
            }
            // 返回代付详情
            if(orderId) {
              prevPage.setData({
                orderId: orderId,
              })
              prevPage.getDetail(orderId)
            }
            // 刷新上一个页面数据
            if (that.data.paramsObj && prevPage) {
              prevPage.onLoad(that.data.paramsObj)
            }
          }
        })
      } else {
        util.showModal({
          title: '错误信息',
          content: res.msg,
          showCancel: false
        })
      }
    }).catch((err) => {
      app.globalData.hasLogin = false
      util.showToast({ title: '登录失败' })
    })
  },
  accountLogin: function() {
    wx.navigateTo({
      url: '/pages/auth/register/register'
    })
  },
  getUserInfo() {
    util.request(api.userInfo).then(res => {
      if (res.data.code == 0) {
        const result = res.data.data
		    util.setUserInfo(res.data.data)
        wx.setStorageSync('baseInfo', res.data.data)
      } else {
        util.showToast({ title: res.msg })
      }
    }).catch((err) => {
      console.log(err)
    })
  },
  noLogin() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  }
})
