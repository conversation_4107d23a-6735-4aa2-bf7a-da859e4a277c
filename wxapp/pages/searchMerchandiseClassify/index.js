const api = require('../../config/api')
const util = require('../../utils/util')
const ic_sort = api.ImgUrl + 'images/<EMAIL>'
const ic_sort_up = api.ImgUrl + 'images/<EMAIL>'
const ic_sort_down = api.ImgUrl + 'images/<EMAIL>'

Page({
  data: {
    categoryId: '',
    classify: '',
    sortImage: ic_sort,
    list: [],
    noData: false,
    sortType: 'default',
    priceSort: 'default',
    priceSortImg: {
      default: ic_sort,
      asc: ic_sort_up,
      desc: ic_sort_down
    }
  },
  onLoad: function(options) {
    this.setData({
      categoryId: options.categoryId,
      classify: options.categoryName
    })
    this.getProductList()
  },
  onShow: function() {
  },
  handleaddCart() {
    this.shopCartNumber()
  },
  changeSort(e) {
    const type = e.currentTarget.dataset.type
    if (type === 'price') {
      let priceSort = ''
      if (this.data.priceSort === 'default') {
        priceSort = 'asc'
      } else if (this.data.priceSort === 'asc') {
        priceSort = 'desc'
      } else if (this.data.priceSort === 'desc') {
        priceSort = 'asc'
      }
      this.setData({
        priceSort
      })
    } else {
      this.setData({
        priceSort: 'default'
      })
    }
    this.setData({
      sortType: type
    })
    this.getProductList()
  },
  getProductList() {
    const params = {
      categoryId: this.data.categoryId,
      page: 1,
      num: 100,
      offset: 1000
    }
    if (this.data.priceSort !== 'default') {
      params.orderBy = 'salePrice'
      params.order = this.data.priceSort
    }
    util.request(api.productList, params, 'get').then(res => {
      this.setData({
        list: res.data.data.result,
        noData: res.data.data.result.length === 0
      })
    })
  },
  shopCartNumber() {
    util.request(api.cartsInfo).then(res => {
      const list = res.data.data.groups[0].items
      let number = 0
      list.forEach(item => {
        number += item.quantity
      })
      const fixedCart = this.selectComponent('#fixedCart')
      fixedCart.setNumber(number)
    })
  }
})
