<navbar isBack="{{true}}" backgroundColor="#fff" navTitle="{{classify}}"></navbar>
<merchandiseSearch showSearchBtn showRelevant></merchandiseSearch>
<view class="condition-bar">
  <text class="condition-item {{sortType === 'default' ? 'active' : ''}}" data-type="default" bind:tap="changeSort">默认排序</text>
  <view class="condition-item {{sortType === 'price' ? 'active' : ''}}" data-type="price" bind:tap="changeSort">
    <text>价格</text>
    <image class="orderby-img" src="{{priceSortImg[priceSort]}}"></image>
  </view>
</view>
<view class="item-wrap" wx:for="{{list}}">
  <merchandise horizontal subTitle detail="{{item}}" customTitle="{{'处方药不展示包装'}}" bind:addCart="handleaddCart"></merchandise>
</view>
<nodata wx:if="{{noData}}"></nodata>
<fixedCart id="fixedCart"></fixedCart>
